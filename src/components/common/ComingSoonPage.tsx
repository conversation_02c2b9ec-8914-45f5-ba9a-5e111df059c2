import React from 'react';
import { Construction, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Button from './Button';

interface ComingSoonPageProps {
  title: string;
  description?: string;
}

const ComingSoonPage: React.FC<ComingSoonPageProps> = ({ 
  title, 
  description = "This feature is currently under development and will be available soon."
}) => {
  const navigate = useNavigate();

  return (
    <div className="min-h-[80vh] flex flex-col items-center justify-center px-4 py-12">
      <div className="text-center max-w-2xl mx-auto">
        {/* Icon */}
        <div className="mb-8">
          <div className="w-24 h-24 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-full flex items-center justify-center mx-auto">
            <Construction className="w-12 h-12 text-yellow-600" />
          </div>
        </div>
        
        {/* Title and Description */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">{title}</h1>
        <p className="text-lg text-gray-600 mb-8">{description}</p>
        
        {/* Back Button */}
        <Button
          onClick={() => navigate('/analytics')}
          variant="outline"
          icon={ArrowLeft}
          className="mt-4 m-auto"
        >
          Back to Dashboard
        </Button>
      </div>
    </div>
  );
};

export default ComingSoonPage;
