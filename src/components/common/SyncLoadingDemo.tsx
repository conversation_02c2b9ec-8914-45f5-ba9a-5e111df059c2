import React, { useState } from 'react';
import { RefreshCw } from 'lucide-react';
import LoadingOverlay from './LoadingOverlay';
import toast from 'react-hot-toast';

const SyncLoadingDemo: React.FC = () => {
  const [showOverlay, setShowOverlay] = useState(false);
  const [syncSuccess, setSyncSuccess] = useState(false);

  const handleSyncDemo = async () => {
    setShowOverlay(true);
    setSyncSuccess(false);
    
    try {
      // Simulate sync operation
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Show success state
      setSyncSuccess(true);
      
      toast.success("Demo sync completed successfully!");
    } catch (error) {
      setShowOverlay(false);
      toast.error("Demo sync failed!");
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Sync Loading Demo
      </h3>
      <p className="text-gray-600 mb-4">
        Click the button below to see the full-page loading overlay with blur effect
        that appears during Zoho sync operations.
      </p>
      
      <button
        onClick={handleSyncDemo}
        disabled={showOverlay}
        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <RefreshCw className="w-4 h-4" />
        Demo Sync Loading
      </button>

      {/* Loading Overlay */}
      <LoadingOverlay
        isVisible={showOverlay}
        message="Setting things up — this won't take long."
        showSuccess={syncSuccess}
        successMessage="Demo sync completed successfully!"
        onComplete={() => {
          setShowOverlay(false);
          setSyncSuccess(false);
        }}
      />
    </div>
  );
};

export default SyncLoadingDemo;
