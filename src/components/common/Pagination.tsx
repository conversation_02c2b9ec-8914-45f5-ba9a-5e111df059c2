import React from "react";
import { getVisiblePages } from "../../utils/getVisiblePages";

interface PaginationProps {
  pageIndex: number;
  pageSize: number;
  totalCount: number;
  onPageChange: (pageIndex: number) => void;
  onPrevious: () => void;
  onNext: () => void;
  canPreviousPage: boolean;
  canNextPage: boolean;
  pageCount: number;
}

const Pagination: React.FC<PaginationProps> = ({
  pageIndex,
  pageSize,
  totalCount,
  onPageChange,
  onPrevious,
  onNext,
  canPreviousPage,
  canNextPage,
  pageCount,
}) => {
  const currentPage = pageIndex + 1;
  const totalPages = pageCount;

  const pages = getVisiblePages(currentPage, totalPages);

  const start = pageIndex * pageSize + 1;
  const end = Math.min((pageIndex + 1) * pageSize, totalCount);

  return (
    <div className="border-t border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-700">
          Showing {start} to {end} of {totalCount} results
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={onPrevious}
            disabled={!canPreviousPage}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50"
          >
            Previous
          </button>
          <div className="flex items-center space-x-1">
            {pages.map((page, i) =>
              page === "..." ? (
                <span
                  key={`ellipsis-${i}`}
                  className="px-3 py-2 text-sm text-gray-500"
                >
                  ...
                </span>
              ) : (
                <button
                  key={page}
                  onClick={() => onPageChange((page as number) - 1)}
                  className={`px-3 py-2 text-sm font-medium rounded-lg ${
                    currentPage === page
                      ? "bg-blue-600 text-white"
                      : "text-gray-500 bg-white border border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  {page}
                </button>
              )
            )}
          </div>
          <button
            onClick={onNext}
            disabled={!canNextPage}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default Pagination;
