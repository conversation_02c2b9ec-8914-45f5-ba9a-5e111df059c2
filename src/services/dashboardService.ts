import api from './api';

// Dashboard Counts Response Interface
export interface DashboardCounts {
  total_leads: number;
  total_franchisors: number;
  total_sms: number;
  total_meetings: number;
  leads_change_percent: number;
  franchisors_change_percent: number;
  sms_change_percent: number;
  meetings_change_percent: number;
}

export interface DashboardCountsResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: DashboardCounts;
  error_code: number;
}

// Recent Activity Response Interfaces
export interface LatestLead {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  status: string;
  created_at: string;
}

export interface LatestFranchisor {
  id: string;
  name: string;
  contactFirstName: string;
  contactLastName: string;
  email: string;
  region: string;
  is_active: boolean;
  created_at: string;
}

export interface LatestQuestion {
  id: string;
  question_text: string;
  category: string;
  is_active: boolean;
  created_at: string;
}

export interface LatestException {
  id: string;
  error_type: string;
  error_message: string;
  endpoint: string;
  user_id: string | null;
  severity: string;
  created_at: string;
}

export interface RecentActivity {
  latest_lead: LatestLead;
  latest_franchisor: LatestFranchisor;
  latest_question: LatestQuestion;
  latest_exception: LatestException;
}

export interface RecentActivityResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: RecentActivity;
  error_code: number;
}

export interface DashboardAnalyticsResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: {
    counts: {
      total_leads: number;
      total_franchisors: number;
      total_questions: number;
      total_meetings: number;
      leads_change_percent: number;
      franchisors_change_percent: number;
      questions_change_percent: number;
      meetings_change_percent: number;
      avg_questions_per_day: number;
      avg_escalations_per_day: number;
    };
    recent_activity: {
      id: string;
      type: string;
      title: string;
      description: string;
      timestamp: string;
      metadata: {
        status: string;
      };
    }[];
    chart_data: {
      date: string;
      question_count: number;
      escalation_count: number;
      escalation_rate: number;
    }[];
    detailed_analytics: {
      date: string;
      question_count: number;
      escalation_count: number;
      escalation_rate: number;
    }[];
    applied_filters: {
      date_range: string;
      time_period: string;
    };
    available_filters: {
      date_ranges: string[];
      time_periods: string[];
    };
    period_label: string;
    generated_at: string;
  };
  error_code: number;
}


// Dashboard Service
export const dashboardService = {
  /**
   * Get dashboard counts
   * Route: GET /dashboard/counts
   */
  getCounts: async (): Promise<DashboardCounts> => {
    const response = await api.get<DashboardCountsResponse>('/dashboard/counts');
    return response.data.data;
  },

  /**
   * Get recent activity
   * Route: GET /dashboard/recent-activity
   */
  getRecentActivity: async (): Promise<RecentActivity> => {
    const response = await api.get<RecentActivityResponse>('/dashboard/recent-activity');
    return response.data.data;
  },

  /**
   * Get dashboard analytics
   * Route: GET /dashboard/analytics
   */
  getDashboardAnalytics: async (
    params?: {
      date_range?: string;
      time_period?: string;
      custom_start?: string;
      custom_end?: string;
    }
  ) => {
    const response = await api.get<DashboardAnalyticsResponse>('/dashboard/analytics', { params });
    return response.data.data;
  },
};
