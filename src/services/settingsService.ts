import api from "./api";

export interface HolidayItem {
  id: string;
  date: string; // YYYY-MM-DD
  description: string;
  holiday_type: 'PREDEFINED' | 'PERSONAL';
  all_day: boolean;
  start_time?: string; // e.g. "09:00:00"
  end_time?: string;   // e.g. "17:00:00"
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
}

export interface HolidayFilters {
  skip?: number;
  limit?: number;
  search?: string;
  holiday_type?: 'PREDEFINED' | 'PERSONAL';
  start_date?: string; // YYYY-MM-DD
  end_date?: string;   // YYYY-MM-DD
}

export interface CreateHolidayPayload {
  date: string;
  description: string;
  holiday_type: 'PREDEFINED' | 'PERSONAL';
  all_day: boolean;
  start_time?: string;
  end_time?: string;
}

export interface UpdateHolidayPayload extends CreateHolidayPayload {
  is_active: boolean;
}

// ------------------------
// Messaging Rules
// ------------------------
export interface MessagingRule {
  id: string;
  lead_init_delay_h: number;
  max_followups: number;
  no_response_delay_h: number;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
}

export interface MessagingRuleFilters {
  skip?: number;
  limit?: number;
  include_inactive?: boolean;
}

export interface CreateMessagingRulePayload {
  lead_init_delay_h: number;
  max_followups: number;
  no_response_delay_h: number;
}

export interface UpdateMessagingRulePayload extends CreateMessagingRulePayload {
  is_active: boolean;
}

export interface GeneralMessage {
  id: string;
  message: string;
  message_type: string;
}

export interface GeneralMessageResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: {
    details: GeneralMessage;
  };
  error_code: null | string;
}

export interface UpdateGeneralMessagePayload {
  message: string;
}

export const settingsService = {
  async getHolidays(filters: HolidayFilters) {
    const response = await api.get('/settings/holidays', { params: filters });
    return response.data;
  },

  async getHolidayById(id: string) {
    const response = await api.get(`/settings/holidays/${id}`);
    return response.data;
  },

  async createHoliday(payload: CreateHolidayPayload) {
    const response = await api.post('/settings/holidays', payload);
    return response.data;
  },

  async updateHoliday(id: string, payload: UpdateHolidayPayload) {
    const response = await api.put(`/settings/holidays/${id}`, payload);
    return response.data;
  },

  async deleteHoliday(id: string) {
    const response = await api.delete(`/settings/holidays/${id}`);
    return response.data;
  },

  // ------------------------
  // Messaging Rules
  // ------------------------

  async getMessagingRules(filters?: MessagingRuleFilters) {
    const response = await api.get('/settings/messaging-rules', { params: filters });
    return response.data?.data?.details?.items[0] || null;
  },

  async getMessagingRuleById(id: string) {
    const response = await api.get(`/settings/messaging-rules/${id}`);
    return response.data;
  },

  async getActiveMessagingRule() {
    const response = await api.get(`/settings/messaging-rules/active`);
    return response.data;
  },

  async createMessagingRule(payload: CreateMessagingRulePayload) {
    const response = await api.post(`/settings/messaging-rules`, payload);
    return response.data?.data?.details || null;
  },

  async updateMessagingRule(id: string, payload: UpdateMessagingRulePayload) {
    const response = await api.put(`/settings/messaging-rules/${id}`, payload);
    return response.data?.data?.details || null;
  },

  async deleteMessagingRule(id: string) {
    const response = await api.delete(`/settings/messaging-rules/${id}`);
    return response.data?.data || null;
  },

  async getMessageByType(messageType: string): Promise<GeneralMessageResponse> {
    const response = await api.get(`/general-messages/type/${messageType}`);
    return response.data;
  },

  async updateByType(
    messageType: string,
    payload: UpdateGeneralMessagePayload
  ): Promise<GeneralMessageResponse> {
    const response = await api.put(`/general-messages/type/${messageType}`, payload);
    return response.data;
  },

};
