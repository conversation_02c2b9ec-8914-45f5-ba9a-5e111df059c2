import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import Login from './components/auth/Login';
import RefreshTokenDialog from './components/auth/RefreshTokenDialog';
import Layout from './components/layout/Layout';
import Dashboard from './components/dashboard/Dashboard';
import Leads from './components/leads/Leads';
import LeadDetails from './components/leads/LeadDetails';
import LeadEdit from './components/leads/LeadEdit';
import Documents from './components/documents/Documents';
import Questions from './components/questions/Questions';
import Analytics from './components/analytics/Analytics';
import SalesScripts from './components/scripts/SalesScripts';
// import BookingLinks from './components/booking/BookingLinks';
import Settings from './components/settings/Settings';
import { Toaster } from 'react-hot-toast';
import Brands from './components/brands/Brands';
import FranchisorDetails from './components/brands/FranchisorDetails';
import FranchisorEdit from './components/brands/FranchisorEdit';
import Industry from './components/industries/Industry';
import VerifyOtp from './components/ForgotPassword/VerifyOtp';
import ResetPassword from './components/ForgotPassword/ResetPassword';
import ForgotPassword from './components/ForgotPassword/ForgotPassword';
import ComingSoonPage from './components/common/ComingSoonPage';
import QuestionBank from './components/questionBank/QuestionBank';
import EscalationQuestions from './components/escalationQuestions/EscalationQuestions';

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" />;
};

const AppRoutes: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <Routes>
      <Route
        path="/login"
        element={isAuthenticated ? <Navigate to="/analytics" /> : <Login />}
      />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/verify-otp" element={<VerifyOtp />} />
      <Route path="/reset-password" element={<ResetPassword />} />
      <Route path="/" element={<Navigate to="/analytics" />} />
      <Route
        path="/*"
        element={
          <ProtectedRoute>
            <Layout />
          </ProtectedRoute>
        }
      >
        <Route path="analytics" element={<Dashboard />} />
        <Route path="franchisors" element={<Brands />} />
        <Route path="franchisors/:id" element={<FranchisorDetails />} />
        <Route path="franchisors/:id/edit" element={<FranchisorEdit />} />
        <Route path="leads" element={<Leads />} />
        <Route path="leads/:id" element={<LeadDetails />} />
        <Route path="leads/:id/edit" element={<LeadEdit />} />
        {/* <Route path="categories" element={<Category />} /> */}
        <Route path="industries" element={<Industry />} />
        {/* <Route path="subcategories" element={<Subcategories />} /> */}
        <Route path="documents" element={<Documents />} />
        <Route path="scripts" element={<SalesScripts />} />
        <Route path="prequalification-questions" element={<Questions />} />
        <Route path="meetings" element={<ComingSoonPage title="Meeting Room" />} />
        <Route path="question-bank" element={<QuestionBank />} />
        <Route path="escalation-questions" element={<EscalationQuestions />} />
        {/* <Route path="analytics" element={<Analytics />} /> */}
        <Route path="settings" element={<Settings />} />
        <Route path='*' element={<Navigate to="/analytics" />} />
      </Route>
    </Routes>
  );
};

function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <AppRoutes />
            <RefreshTokenDialog />
            <Toaster position="top-right" />
          </div>
        </Router>
      </ThemeProvider>
    </AuthProvider>
  );
}

export default App;
