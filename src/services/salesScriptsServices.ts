import api from "./api";


export interface Script {
  id: string;
  script_title: string;
  script_content: string;
  script_stage: string;
  is_active: boolean;
  order_sequence: number;
};

export const salesScriptsServices = {
  // Sales Script GET
  getSalesScripts: async () => {
    const res = await api.get("/sales-scripts/");
    return res.data.data;
  },

  // Sales Script PUT
  updateSalesScript: async (scriptId: string, payload:Script) => {
    const res = await api.put(`/sales-scripts/${scriptId}`, payload);
    return res.data;
  },
};
