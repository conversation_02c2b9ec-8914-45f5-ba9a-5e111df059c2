{"name": "crm-admin-panel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@tanstack/react-table": "^8.21.3", "axios": "^1.10.0", "formik": "^2.4.5", "input-otp": "^1.4.2", "lodash.debounce": "^4.0.8", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-datepicker": "^8.4.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.22.3", "recharts": "^2.7.2", "yup": "^1.3.3"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/lodash.debounce": "^4.0.9", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}