import React, { useState, useEffect } from 'react';
import { Download, FileText, Loader2, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import { sampleDocsService, SampleDocument } from '../../services/sampleDocsService';

const SampleCsvSection: React.FC = () => {
  const [sampleDocs, setSampleDocs] = useState<SampleDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [downloadingId, setDownloadingId] = useState<string | null>(null);

  // Load sample documents on component mount
  useEffect(() => {
    loadSampleDocs();
  }, []);

  const loadSampleDocs = async () => {
    setLoading(true);
    try {
      const docs = await sampleDocsService.getSampleDocs();
      setSampleDocs(docs);
    } catch (error) {
      console.error('Error loading sample documents:', error);
      toast.error('Failed to load sample CSV files');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (doc: SampleDocument) => {
    setDownloadingId(doc.id);
    try {
      // Extract filename from title or use a default
      const filename = doc.title.toLowerCase().includes('csv') 
        ? `${doc.title.replace(/\s+/g, '-').toLowerCase()}.csv`
        : `${doc.title.replace(/\s+/g, '-').toLowerCase()}.csv`;
      
      await sampleDocsService.downloadSampleDoc(doc.url, filename);
      toast.success(`${doc.title} downloaded successfully`);
    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error(`Failed to download ${doc.title}`);
    } finally {
      setDownloadingId(null);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
          <FileText className="w-5 h-5 text-green-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Sample CSV Downloads</h2>
          <p className="text-sm text-gray-500">Download sample CSV files for data import</p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="p-4 bg-blue-50 rounded-lg flex items-start space-x-3">
          <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <p className="text-sm text-gray-700">
              Use these sample CSV files as templates for importing your data. 
              Make sure to follow the exact column structure and data format shown in the samples.
            </p>
          </div>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Loading skeleton cards */}
            {[1, 2].map((index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-lg p-4 animate-pulse"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                    <div>
                      <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-20"></div>
                    </div>
                  </div>
                  <div className="h-8 bg-gray-200 rounded w-24"></div>
                </div>
              </div>
            ))}
          </div>
        ) : sampleDocs.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-gray-900 mb-1">No Sample Files Available</h3>
            <p className="text-gray-500">Sample CSV files are currently not available.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {sampleDocs.map((doc) => (
              <div
                key={doc.id}
                className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                      <FileText className="w-4 h-4 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{doc.title}</h3>
                      <p className="text-sm text-gray-500">CSV Template</p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleDownload(doc)}
                    disabled={downloadingId === doc.id}
                    className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 transition-colors"
                  >
                    {downloadingId === doc.id ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        <span className="text-sm">Downloading...</span>
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4" />
                        <span className="text-sm">Download</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {sampleDocs.length > 0 && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">How to use sample CSV files:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Download the appropriate sample CSV file for your data type</li>
              <li>• Open the file in Excel, Google Sheets, or any spreadsheet application</li>
              <li>• Replace the sample data with your actual data</li>
              <li>• Keep the column headers exactly as shown in the sample</li>
              <li>• Save the file as CSV format and import it into the system</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default SampleCsvSection;
