// NOTE: In this codebase, 'franchise' and 'brand' refer to the same thing, and so do 'category' and 'industry'.
// The UI displays 'Brand' and 'Industry', but variable/function names may still use 'franchise' and 'category' for legacy reasons..

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Search, Upload, RefreshCw, Eye, Edit, Loader2, ArrowUpDown, ArrowUp, ArrowDown, Filter } from 'lucide-react';
import toast from 'react-hot-toast';
import { leadService, Lead, LeadSource, LeadStatus } from '../../services/leadService';
import 'react-datepicker/dist/react-datepicker.css';
import DatePicker from 'react-datepicker';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
} from "@tanstack/react-table";
import { FranchisorDropdown, questionsService } from '../../services/questionsService';
import LoadingOverlay from '../common/LoadingOverlay';
import Select from '../common/Select';
import Pagination from '../common/Pagination';
// Update the Lead interface to include notes
interface LeadWithNotes extends Lead {
  notes?: string;
}

const Leads: React.FC = () => {
  const navigate = useNavigate();
  const isRequestInProgress = useRef(false);
  const [leads, setLeads] = useState<Lead[]>([]);
  const [loading, setLoading] = useState({
      table: false,
      page: true,
      actions: false,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showModal, setShowModal] = useState(false);
  const [editingLead, setEditingLead] = useState<Lead | null>(null);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [franchisors, setFranchisors] = useState<FranchisorDropdown[]>([]);
  const [leadSources, setLeadSources] = useState<LeadSource[]>([]);
  const [leadStatuses, setLeadStatuses] = useState<LeadStatus[]>([]);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [savingLead, setSavingLead] = useState(false);
  const [togglingStatus, setTogglingStatus] = useState<string | null>(null);
  const [leadSourceFilter, setLeadSourceFilter] = useState("all");
  const [createdFrom, setCreatedFrom] = useState<Date | null>(null);
  const [createdTo, setCreatedTo] = useState<Date | null>(null);
  const [sortOrder, setSortOrder] = useState<string>("created_desc");
  const [isSyncing, setIsSyncing] = useState(false);
  const [showSyncOverlay, setShowSyncOverlay] = useState(false);
  const [syncSuccess, setSyncSuccess] = useState(false);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [totalItems, setTotalItems] = useState(0);
  const [sorting, setSorting] = useState<any[]>([]);

    // Initial data load
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading((prev) => ({ ...prev, page: true, table: true }));

        // Load leads first
        const leadsResponse = await leadService.getLeads({
          skip: pagination.pageIndex * pagination.pageSize,
          limit: pagination.pageSize,
        });

        if (leadsResponse?.success) {
          setLeads(leadsResponse?.data?.items || []);
          setTotalItems(leadsResponse?.data?.total_count || 0);
          setLoading((prev) => ({ ...prev, page: false }));
        }

        // Load dropdowns
        // Load dropdown data sequentially for better debugging
        console.log('🔄 Starting to fetch lead sources...');
        const sourcesRes = await leadService.getLeadSources();
        console.log('🔄 Starting to fetch lead statuses...');
        const statusesRes = await leadService.getLeadStatuses();
        console.log('🔄 Starting to fetch franchisors...');
        const franchisorsRes = await questionsService.getFranchisorsDropdown().catch(err => {
          console.error('Franchisors API Error:', err);
          return { success: false, error: err };
        });

        console.log('🔍 Sources Response:', sourcesRes);
        console.log('🔍 Statuses Response:', statusesRes);

        // Handle lead sources
        console.log('🔍 sourcesRes structure:', typeof sourcesRes, Object.keys(sourcesRes || {}));
        if (sourcesRes?.success && 'data' in sourcesRes) {
          console.log('✅ Setting lead sources:', sourcesRes.data);
          console.log('✅ Lead sources is array?', Array.isArray(sourcesRes.data));
          console.log('✅ Lead sources length:', sourcesRes.data?.length);
          setLeadSources(sourcesRes.data);
        } else if (sourcesRes && 'items' in sourcesRes) {
          // Handle direct API response format
          console.log('✅ Setting lead sources from items:', sourcesRes.items);
          setLeadSources(Array.isArray(sourcesRes.items) ? sourcesRes.items : []);
        } else {
          console.error('❌ Lead sources API failed:', sourcesRes);
          setLeadSources([]);
        }

        // Handle lead statuses
        console.log('🔍 statusesRes structure:', typeof statusesRes, Object.keys(statusesRes || {}));
        if (statusesRes?.success && 'data' in statusesRes) {
          console.log('✅ Setting lead statuses:', statusesRes.data);
          console.log('✅ Lead statuses is array?', Array.isArray(statusesRes.data));
          console.log('✅ Lead statuses length:', statusesRes.data?.length);
          setLeadStatuses(statusesRes.data);
        } else if (statusesRes && 'items' in statusesRes) {
          // Handle direct API response format
          console.log('✅ Setting lead statuses from items:', statusesRes.items);
          setLeadStatuses(Array.isArray(statusesRes.items) ? statusesRes.items : []);
        } else {
          console.error('❌ Lead statuses API failed:', statusesRes);
          setLeadStatuses([]);
        }

        if (franchisorsRes?.success && 'data' in franchisorsRes) {
          setFranchisors(franchisorsRes.data.details.franchisors);
        } else {
          console.error('Franchisors API failed:', franchisorsRes);
          setFranchisors([]);
        }

      } catch (error) {
        console.error("Error loading initial data:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to load data";
        toast.error(errorMessage);
      } finally {
        setLoading((prev) => ({ ...prev, page: false, table: false }));
      }
    };
    loadInitialData();
  }, []);

      useEffect(() => {
      const loadFranchisorsForDropdown = async () => {
        setLoading((prev) => ({ ...prev, actions: true }));
        try {
          const franchisorsResponse =
            await questionsService.getFranchisorsDropdown();
          if (
            franchisorsResponse.success &&
            franchisorsResponse.data?.details?.franchisors
          ) {
            setFranchisors(franchisorsResponse.data.details.franchisors);
          }
        } catch (error) {
          if (
            typeof error === "object" &&
            error !== null &&
            ("name" in error || "code" in error)
          ) {
            if (
              (error as { name?: string }).name === "AbortError" ||
              (error as { code?: string }).code === "ERR_CANCELED" ||
              (error as { code?: string }).code === "ECONNABORTED"
            ) {
              return;
            }
          }
          console.error("Error loading brands:", error);
          toast.error("Failed to load brands");
        } finally {
          setLoading((prev) => ({ ...prev, actions: false }));
        }
      };
  
      // Load brands for dropdown on initial mount
      loadFranchisorsForDropdown();
    }, []);

      // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

      // Get franchise name by ID
  const getFranchiseName = useCallback((franchiseId: string) => {
    if (loading.actions) {
      return "Loading...";
    }
    const franchise = franchisors.find((f) => f.id === franchiseId);
    return franchise ? franchise.name : "Unknown Franchisor";
  }, [loading.actions, franchisors]);


const fetchLeads = useCallback(async () => {
  if (isRequestInProgress.current) return;
  
  try {
    isRequestInProgress.current = true;
    setLoading(prev => ({ ...prev, table: true }));

    // Convert sorting to API format
    let sortParam = sortOrder;
    if (sorting.length > 0) {
      const sort = sorting[0];
      sortParam = `${sort.id}_${sort.desc ? 'desc' : 'asc'}`;
    }

    const params = {
      skip: pagination.pageIndex * pagination.pageSize,
      limit: pagination.pageSize,
      search: debouncedSearchTerm || undefined,
      lead_status_id: statusFilter !== "all" ? statusFilter : undefined,
      lead_source_id: leadSourceFilter !== "all" ? leadSourceFilter : undefined,
      created_from: createdFrom ? createdFrom.toISOString() : undefined,
      created_to: createdTo ? createdTo.toISOString() : undefined,
      sort: sortParam,
    };

    const res = await leadService.getLeads(params);
    
    if (res?.success) {
      setLeads(res?.data?.items || []);
      setTotalItems(res?.data?.total_count || 0);

      const totalPages = Math.ceil(res.data.total_count / pagination.pageSize);
      if (pagination.pageIndex >= totalPages && totalPages > 0) {
        setPagination(prev => ({ ...prev, pageIndex: totalPages - 1 }));
      }
    }
  } catch (err) {
    toast.error("Failed to load leads.");
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  } finally {
    setLoading(prev => ({ ...prev, table: false }));
    isRequestInProgress.current = false;
  }
}, [
  pagination.pageIndex,
  pagination.pageSize,
  debouncedSearchTerm,
  statusFilter,
  leadSourceFilter,
  createdFrom,
  createdTo,
  sortOrder,
  sorting,
]);

// RLoad filtered/paginated leads
useEffect(() => {
  if (!loading.page) { // Don't load while initial load is in progress
    fetchLeads();
  }
}, [fetchLeads, loading.page]);

  const formatDateForApi = (date: Date | null) =>
  date ? date.toISOString() : undefined;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'qualified': return 'bg-green-100 text-green-800';
      case 'contacted': return 'bg-orange-100 text-orange-800';
      case 'converted': return 'bg-purple-100 text-purple-800';
      case 'call-back': return 'bg-yellow-100 text-yellow-800';
      case 'not-interested': return 'bg-red-100 text-red-800';
      case 'no-answer': return 'bg-gray-100 text-gray-800';
      case 'answering-machine': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'csv': return '📁';
      case 'zoho': return '🔄';
      case 'manual': return '✍️';
      case 'website': return '🌐';
      default: return '📋';
    }
  };

  // Add/Edit Lead handler
  const handleAddLead = async (leadData: any) => {
    try {
      setSavingLead(true);

      // Convert budget_preference from string to number
      const processedData = {
        ...leadData,
        budget_preference: leadData.budget_preference === '' ? null : Number(leadData.budget_preference)
      };

      if (editingLead) {
        // Update
        const updated = await leadService.updateLead(editingLead.id, processedData);
        setLeads(prev => prev.map(l => l.id === updated.data.id ? updated.data : l));
        toast.success('Lead updated successfully!');
      } else {
        // Create
        const created = await leadService.createLead(processedData);
        setLeads(prev => [created.data, ...prev]);
        toast.success('Lead created successfully!');
      }
      setShowModal(false);
      setEditingLead(null);
    } catch (err) {
      toast.error('Failed to save lead.');
    } finally {
      setSavingLead(false);
    }
  };

  // Edit handler
  const handleEdit = (lead: Lead) => {
    setEditingLead(lead);
    setShowModal(true);
  };

  // Delete handler
  const handleDelete = async (lead: Lead) => {
    if (!window.confirm('Are you sure you want to delete this lead?')) return;
    try {
      await leadService.deleteLead(lead.id);
      toast.success('Lead deleted successfully!');
      fetchLeads();
    } catch (err) {
      toast.error('Failed to delete lead.');
    }
  };

  // View details handler (fetch latest if needed)
  const handleViewLead = async (lead: Lead) => {
    try {
      const res = await leadService.getLeadById(lead.id);
      setSelectedLead(res.data);
    } catch (err) {
      toast.error('Failed to fetch lead details.');
    }
  };

  // Handler for Import CSV
  const handleImportCsvClick = () => {
    if (fileInputRef.current) fileInputRef.current.value = '';
    fileInputRef.current?.click();
  };

  const handleCsvFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
      toast.error("Please select a valid CSV file.");
      return;
    }
    setUploading(true);
    try {
      const res = await leadService.bulkUploadLeads(file);
      toast.success(res.message?.description || "Bulk upload completed!");
      fetchLeads();
    } catch (err: any) {
      console.error("Error importing CSV:", err);

      // Handle specific error messages from API response
      let errorMessage = "Bulk upload failed.";

      if (err?.response?.data) {
        const responseData = err.response.data;

        // Check for different possible error message formats
        if (responseData.detail) {
          errorMessage = responseData.detail;
        } else if (responseData.message?.description) {
          errorMessage = responseData.message.description;
        } else if (responseData.message?.title) {
          errorMessage = responseData.message.title;
        } else if (responseData.message) {
          errorMessage = typeof responseData.message === 'string' ? responseData.message : errorMessage;
        } else if (responseData.error) {
          errorMessage = responseData.error;
        }
      } else if (err?.message) {
        errorMessage = err.message;
      }

      toast.error(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const handleToggleStatus = async (id: string, currentStatus: boolean) => {
    try {
      setTogglingStatus(id);
      const success = await leadService.toggleStatus(id, !currentStatus);
      if (success) {
        setLeads((prev) =>
          prev.map((f) =>
            f.id === id ? { ...f, is_active: !currentStatus } : f
          )
        );
        toast.success(
          `Lead ${!currentStatus ? "activated" : "deactivated"} successfully!`
        );
      } else {
        toast.error("Failed to update lead status. Please try again.");
      }
      } catch (err) {
      console.error('Failed to toggle lead status:', err);
      toast.error('Failed to update lead status. Please try again.');
    } finally {
      setTogglingStatus(null);
    }
  };

  const columns = React.useMemo<ColumnDef<Lead, unknown>[]>(
    () => [
      {
        id: "name",
        header: ({ column }) => {
          return (
            <div
              className={`flex items-center space-x-2 ${
                column.getCanSort()
                  ? 'cursor-pointer select-none hover:text-blue-600'
                  : ''
              }`}
              onClick={column.getToggleSortingHandler()}
            >
              <span>Lead Info</span>
              {column.getCanSort() && (
                <span className="flex flex-col">
                  {column.getIsSorted() === 'asc' ? (
                    <ArrowUp className="w-4 h-4 text-blue-600" />
                  ) : column.getIsSorted() === 'desc' ? (
                    <ArrowDown className="w-4 h-4 text-blue-600" />
                  ) : (
                    <ArrowUpDown className="w-4 h-4 text-gray-400" />
                  )}
                </span>
              )}
            </div>
          );
        },
        enableSorting: true,
        accessorFn: (row) => `${row.first_name || ''} ${row.last_name || ''}`.trim() || '-',
        cell: (info) => {
          const firstName = info.row.original.first_name;
          const lastName = info.row.original.last_name;
          const fullName = `${firstName || ''} ${lastName || ''}`.trim();
          const zohoId = info.row.original.zoho_lead_id;

          return (
            <div>
              <div className="font-medium text-gray-900">
                {fullName || '-'}
              </div>
              <div className="text-sm text-gray-500">
                {zohoId ? `Zoho: ${zohoId}` : `ID: ${info.row.original.id}`}
              </div>
            </div>
          );
        },
      },
      {
        id: "contact",
        header: "Contact",
        cell: () => (
          <div className="text-sm text-gray-900">
            -
          </div>
        ),
      },
      {
        accessorKey: "lead_source_name",
        header: "Source",
        cell: (info) => (
          <span className="capitalize text-gray-700">
            {info.row.original.lead_source_name || '-'}
          </span>
        ),
      },
      {
        accessorKey: "lead_status_name",
        header: "Status",
        cell: (info) => (
          <div className="flex items-center space-x-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: info.row.original.lead_status_colour || '#gray' }}
            />
            <span className="text-sm text-gray-700">
              {info.row.original.lead_status_name || '-'}
            </span>
          </div>
        ),
      },
      {
        accessorKey: "brand_preference",
        header: "Interest",
        cell: (info) => {
          return (
            <span className="text-gray-700">
              {getFranchiseName(info.getValue() as string) || "No Interest"}
            </span>
          );
        },
      },
      {
        accessorKey: "is_active",
        header: "Active",
        cell: (info) => {
          const lead = info.row.original;
          return (
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handleToggleStatus(lead.id, lead.is_active)}
                disabled={togglingStatus === lead.id}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  lead.is_active ? "bg-blue-600" : "bg-gray-200"
                } ${
                  togglingStatus === lead.id
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    lead.is_active ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          );
        },
      },
      {
        id: "created_at",
        header: ({ column }) => {
          return (
            <div
              className={`flex items-center space-x-2 ${
                column.getCanSort()
                  ? 'cursor-pointer select-none hover:text-blue-600'
                  : ''
              }`}
              onClick={column.getToggleSortingHandler()}
            >
              <span>Created</span>
              {column.getCanSort() && (
                <span className="flex flex-col">
                  {column.getIsSorted() === 'asc' ? (
                    <ArrowUp className="w-4 h-4 text-blue-600" />
                  ) : column.getIsSorted() === 'desc' ? (
                    <ArrowDown className="w-4 h-4 text-blue-600" />
                  ) : (
                    <ArrowUpDown className="w-4 h-4 text-gray-400" />
                  )}
                </span>
              )}
            </div>
          );
        },
        enableSorting: true,
        accessorKey: "created_at",
        cell: (info) => {
          const createdAt = info.row.original.created_at;
          return (
            <div className="text-sm text-gray-700">
              {createdAt ? new Date(createdAt).toLocaleDateString() : '-'}
            </div>
          );
        },
      },
      {
        id: "actions",
        header: "Actions",
        cell: (info) => {
          const lead = info.row.original;
          return (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => navigate(`/leads/${lead.id}`)}
                className="text-blue-600 hover:text-blue-800 p-1"
                title="View Details"
              >
                <Eye className="w-4 h-4 text-blue-600" />
              </button>
              <button
                onClick={() => navigate(`/leads/${lead.id}/edit`)}
                className="text-green-600 hover:text-green-800 p-1"
                title="Edit Lead"
              >
                <Edit className="w-4 h-4 text-green-600" />
              </button>
            </div>
          );
        },
      },
    ],
    [togglingStatus, getFranchiseName]
  );

  const table = useReactTable({
    data: leads,
    columns,
    pageCount: totalItems > 0 ? Math.ceil(totalItems / pagination.pageSize) : 1,
    state: {
      pagination: {
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
      },
      sorting,
    },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
  });

  const isFilterActive = searchTerm.trim() !== "" || statusFilter !== "" || leadSourceFilter !== "";

  if (loading.page && leads.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading leads...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Lead Management</h1>
          <p className="text-gray-600 mt-1">Manage and track all your leads and communications</p>
        </div>
        <div className="flex space-x-3">
         
               <button
            className={`bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 ${
              isSyncing ? "opacity-70 cursor-not-allowed" : ""
            }`}
            disabled={isSyncing}
            onClick={async () => {
              setIsSyncing(true);
              setShowSyncOverlay(true);
              setSyncSuccess(false);

              try {
                const response = await leadService.syncWithZoho();
                fetchLeads();

                // Show success state
                setSyncSuccess(true);

                toast.success(
                  response.message.description || "Zoho sync successful!"
                );
              } catch (error) {
                setShowSyncOverlay(false);
                toast.error(error.message || "Failed to sync with Zoho.");
              } finally {
                setIsSyncing(false);
              }
            }}
          >
            {isSyncing ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Syncing...</span>
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4" />
                <span>Sync Zoho</span>
              </>
            )}
          </button>
          <button
            onClick={() => setShowModal(true)}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Add Lead</span>
          </button>

          <button
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
            onClick={handleImportCsvClick}
            disabled={uploading}
            type="button"
          >
            <Upload className="w-4 h-4" />
            <span>{uploading ? 'Uploading...' : 'Import CSV'}</span>
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv,text/csv"
              style={{ display: 'none' }}
              onChange={handleCsvFileChange}
              disabled={uploading}
            />
          </button>
    
          
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search leads..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <Select
            value={statusFilter || "all"}
            // onChange={setStatusFilter}
            onChange={(value) => {
              setStatusFilter(value === "all" ? "" : value);
              setPagination((prev) => ({ ...prev, pageIndex: 0 }));
            }}
            placeholder='Select Status'
            options={[
              { value: "all", label: "All Status" },
              ...(Array.isArray(leadStatuses) ? (
                console.log('🔍 Rendering status options:', leadStatuses.length, leadStatuses),
                leadStatuses.map(status => ({
                  value: status.id,
                  label: status.name
                }))
              ) : (
                console.log('❌ leadStatuses is not array:', leadStatuses),
                []
              ))
            ]}
          />
          <Select
            value={leadSourceFilter || "all"}
            // onChange={setLeadSourceFilter}
            onChange={(value) => {
              setLeadSourceFilter(value === "all" ? "" : value);
              setPagination((prev) => ({ ...prev, pageIndex: 0 }));
            }}
            placeholder='Select Lead Source'
            options={[
              { value: "all", label: "All Sources" },
              ...(Array.isArray(leadSources) ? (
                console.log('🔍 Rendering source options:', leadSources.length, leadSources),
                leadSources.map(source => ({
                  value: source.id,
                  label: source.name
                }))
              ) : (
                console.log('❌ leadSources is not array:', leadSources),
                []
              ))
            ]}
          />
         {/* Clear Filters Button */}
          <div className="w-full md:w-auto flex-none">
            <button
              onClick={()=>{
                setSearchTerm("");
                setStatusFilter("");
                setLeadSourceFilter("");
                setPagination({ pageIndex: 0, pageSize: 10 });
              }}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 w-full md:w-auto justify-center relative"
            >
              <Filter className="w-4 h-4" />
              <span>Clear Filters</span>

              {isFilterActive && (
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
              )}
            </button>
          </div>
          
        </div>
      </div>

      {/* Leads List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 relative">
        {loading.table ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading leads...</span>
          </div>
        ) : leads.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg">No leads found</div>
            <div className="text-gray-400 text-sm mt-1">Try adjusting your search or filters</div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th
                          key={header.id}
                          className="text-left py-4 px-6 font-medium text-gray-900"
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {table.getRowModel()?.rows?.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50">
                      {row.getVisibleCells()?.map((cell) => (
                        <td key={cell.id} className="py-4 px-6">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <Pagination
              pageIndex={pagination.pageIndex}
              pageSize={pagination.pageSize}
              totalCount={totalItems}
              pageCount={table.getPageCount()}
              onPageChange={(page) => setPagination((prev) => ({ ...prev, pageIndex: page }))}
              onPrevious={() => table.previousPage()}
              onNext={() => table.nextPage()}
              canPreviousPage={table.getCanPreviousPage()}
              canNextPage={table.getCanNextPage()}
            />
          </>
        )}
      </div>

      {/* Add Lead Modal */}
      {showModal && (
        <AddLeadModal
          onClose={() => { setShowModal(false); setEditingLead(null); }}
          onSave={handleAddLead}
          editingLead={editingLead}
          saving={savingLead}
          franchisors={franchisors}
          leadSources={leadSources}
          leadStatuses={leadStatuses}
        />
      )}

      {/* Lead Details Modal */}
      {selectedLead && (
        <LeadDetailsModal
          lead={selectedLead}
          onClose={() => setSelectedLead(null)}
          getFranchiseName={getFranchiseName}
        />
      )}

      {/* Loading Overlay for Sync */}
      <LoadingOverlay
        isVisible={showSyncOverlay}
        message="Setting things up — this won't take long."
        showSuccess={syncSuccess}
        successMessage="Zoho sync completed successfully!"
        onComplete={() => {
          setShowSyncOverlay(false);
          setSyncSuccess(false);
        }}
      />
    </div>
  );
};

const AddLeadModal: React.FC<{
  onClose: () => void;
  onSave: (lead: any) => void;
  editingLead?: Lead | null;
  saving?: boolean;
  franchisors: FranchisorDropdown[];
  leadSources: LeadSource[];
  leadStatuses: LeadStatus[];
}> = ({ onClose, onSave, editingLead, saving, franchisors, leadSources, leadStatuses }) => {
  const [formData, setFormData] = useState({
    first_name: editingLead?.first_name || '',
    last_name: editingLead?.last_name || '',
    email: editingLead?.email || '',
    phone: editingLead?.phone || '',
    mobile: editingLead?.mobile || '',
    location: editingLead?.location || '',
    postal_code: editingLead?.postal_code || '',
    lead_source_id: editingLead?.lead_source_id || '',
    lead_status_id: editingLead?.lead_status_id || '',
    brand_preference: editingLead?.brand_preference || '',
    budget_preference: editingLead?.budget_preference || '',
    zoho_lead_id: editingLead?.zoho_lead_id || '',
    is_active: editingLead?.is_active ?? true,
    is_deleted: editingLead?.is_deleted ?? false,
  });

  useEffect(() => {
    if (editingLead) {
      setFormData({
        first_name: editingLead.first_name,
        last_name: editingLead.last_name || '',
        email: editingLead.email,
        phone: editingLead.phone,
        mobile: editingLead.mobile,
        location: editingLead.location,
        postal_code: editingLead.postal_code || '',
        lead_source_id: editingLead.lead_source_id || '',
        lead_status_id: editingLead.lead_status_id,
        brand_preference: editingLead.brand_preference,
        budget_preference: editingLead.budget_preference?.toString() || '',
        zoho_lead_id: editingLead.zoho_lead_id || '',
        is_active: editingLead.is_active,
        is_deleted: editingLead.is_deleted,
      });
    } else {
      setFormData(f => ({
        ...f,
        is_active: true,
        is_deleted: false,
        lead_source: 'manual',
      }));
    }
  }, [editingLead]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            {editingLead ? 'Edit Lead' : 'Add New Lead'}
          </h2>
        </div>
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
              <input
                type="text"
                value={formData.first_name}
                onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
              <input
                type="text"
                value={formData.last_name}
                onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone *</label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => {
                  const digitsOnly = e.target.value?.replace(/\D/g, '');
                  const truncatedValue = digitsOnly?.slice(0, 10);
                  setFormData({ ...formData, phone: truncatedValue })
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
                maxLength={10}
                minLength={10}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Mobile *</label>
              <input
                type="tel"
                value={formData.mobile}
                onChange={(e) => {
                  const digitsOnly = e.target.value?.replace(/\D/g, '');
                  const truncatedValue = digitsOnly?.slice(0, 10);
                  setFormData({ ...formData, mobile: truncatedValue })
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
                maxLength={10}
                minLength={10}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Postal Code</label>
              <input
                type="text"
                value={formData.postal_code}
                onChange={(e) => setFormData({ ...formData, postal_code: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Lead Source *</label>
              <select
                value={formData.lead_source_id}
                onChange={(e) => setFormData({ ...formData, lead_source_id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select Lead Source</option>
                {Array.isArray(leadSources) && leadSources.map((source) => (
                  <option key={source.id} value={source.id}>
                    {source.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Franchisor *
              </label>
              <select
                value={formData.brand_preference}
                onChange={(e) => setFormData({ ...formData, brand_preference: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select a Franchisor</option>
                {Array.isArray(franchisors) && franchisors.map((franchisor) => (
                  <option key={franchisor.id} value={franchisor.id}>
                    {franchisor.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Status *</label>
              <select
                value={formData.lead_status_id}
                onChange={(e) => setFormData({ ...formData, lead_status_id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select Status</option>
                {Array.isArray(leadStatuses) && leadStatuses.map((status) => (
                  <option key={status.id} value={status.id}>
                    {status.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">How much funds do you have access to?</label>
              <input
                type="number"
                value={formData.budget_preference}
                onChange={(e) => setFormData({ ...formData, budget_preference: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter amount"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Zoho Lead ID</label>
              <input
                type="text"
                value={formData.zoho_lead_id}
                onChange={(e) => setFormData({ ...formData, zoho_lead_id: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            {/* <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Additional Information</label>
              <textarea
                value={formData.additional_info}
                onChange={(e) => setFormData({ ...formData, additional_info: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={4}
                placeholder="Enter any additional information here..."
              />
            </div> */}
          </div>
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              disabled={saving}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>{editingLead ? 'Updating...' : 'Creating...'}</span>
                </>
              ) : (
                <span>{editingLead ? 'Update' : 'Create'}</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const LeadDetailsModal: React.FC<{
  lead: Lead;
  onClose: () => void;
  getFranchiseName: (franchiseId: string) => string;
}> = ({ lead, onClose, getFranchiseName }) => {
  // Function to format notes with bullet points
  const formatNotes = (notes: string | undefined) => {
    if (!notes) return [];
    return notes.split('\n').filter(line => line.trim().length > 0);
  };
  // Function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'qualified': return 'bg-green-100 text-green-800';
      case 'contacted': return 'bg-orange-100 text-orange-800';
      case 'converted': return 'bg-purple-100 text-purple-800';
      case 'call-back': return 'bg-yellow-100 text-yellow-800';
      case 'not-interested': return 'bg-red-100 text-red-800';
      case 'no-answer': return 'bg-gray-100 text-gray-800';
      case 'answering-machine': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Lead Details</h2>
        </div>
        <div className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">Name</label>
              <p className="text-lg font-medium text-gray-900">{lead.full_name ?? "-"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Email</label>
              <p className="text-lg text-gray-900">{lead.email ?? "N/A"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Phone</label>
              <p className="text-lg text-gray-900">{lead.phone ?? "N/A"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Postal Code</label>
              <p className="text-lg text-gray-900">{lead.location ?? "N/A"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Source</label>
              <p className="text-lg text-gray-900 capitalize">{lead.lead_source ?? "No Source"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Status</label>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(lead.status)}`}>
                {(lead.status)?.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') || "No Status"}
              </span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Franchisor ID</label>
              <p className="text-lg text-gray-900">{lead.brand_preference ?? "Unknown Franchisor"}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Franchisor Name</label>
                <p>
                  {getFranchiseName(lead.brand_preference) || "No brand specified"}
                </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Available Funds</label>
              <p className="text-lg text-gray-900">${lead.budget_preference?.toLocaleString() || 'Not specified'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Active Status</label>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${lead.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>{lead.is_active ? 'Active' : 'Inactive'}</span>
            </div>
          </div>
          {/* Notes Section with Bullet Points */}
          {('notes' in lead) && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Notes</h3>
              <div className="p-4 bg-gray-50 rounded-lg">
                {lead.notes ? (
                  <ul className="space-y-2 list-disc pl-5">
                    {formatNotes((lead as any).notes).map((note: string, index: number) => (
                      <li key={index} className="text-sm text-gray-700">{note}</li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-500 text-center py-2">No notes available</p>
                )}
              </div>
            </div>
          )}
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Leads;
