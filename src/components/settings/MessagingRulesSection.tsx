import React, { useEffect, useState } from 'react';
import { Save, Edit, Trash2, Settings as SettingsIcon } from 'lucide-react';
import toast from 'react-hot-toast';
import { settingsService } from '../../services/settingsService';

interface MessagingRuleForm {
  id?: string;
  lead_init_delay_h: number;
  max_followups: number;
  no_response_delay_h: number;
  is_active?: boolean;
}

const MessagingRulesSection: React.FC = () => {
  const [rule, setRule] = useState<MessagingRuleForm | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    fetchActiveRule();
  }, []);

  const fetchActiveRule = async () => {
    try {
      setIsLoading(true);
      const activeRule = await settingsService.getMessagingRules();
      if (activeRule?.id) {
        setRule(activeRule);
        setIsEditing(false);
      } else {
        setRule(null);
      }
    } catch (error: unknown) {
      console.error('Fetch error:', error);
      toast.error('Failed to fetch rule');
      setRule(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!rule) return;
    const { name, value, type, checked } = e.target;
    
    if (type === 'number') {
      const numValue = Number(value);
      if (value !== '' && numValue < 0) {
        toast.error('Value cannot be negative');
        return;
      }
      
      setRule({
        ...rule,
        [name]: value === '' ? 0 : numValue,
      });
    } else {
      setRule({
        ...rule,
        [name]: type === 'checkbox' ? checked : value,
      });
    }
  };

  const isFormValid = () => {
    if (!rule) return false;
    return (
      rule.lead_init_delay_h >= 0 &&
      rule.max_followups >= 0 &&
      rule.no_response_delay_h >= 0
    );
  };

  const handleCreate = async () => {
    try {
      setIsLoading(true);
      if (!rule) return;
      
      const createPayload = {
        lead_init_delay_h: rule.lead_init_delay_h,
        max_followups: rule.max_followups,
        no_response_delay_h: rule.no_response_delay_h
      };
      
      await settingsService.createMessagingRule(createPayload);
      toast.success('Rule created successfully');
      setIsEditing(false);
      await fetchActiveRule();
    } catch (error: unknown) {
      console.error('Create error:', error);
      toast.error('Failed to create rule');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdate = async () => {
    try {
      if (!rule?.id) return;
      setIsLoading(true);
      const updatePayload = {
        lead_init_delay_h: rule.lead_init_delay_h,
        max_followups: rule.max_followups,
        no_response_delay_h: rule.no_response_delay_h,
        is_active: Boolean(rule.is_active)
      };
      
      await settingsService.updateMessagingRule(rule.id, updatePayload);
      toast.success('Rule updated successfully');
      setIsEditing(false);
      await fetchActiveRule();
    } catch (error: unknown) {
      console.error('Update error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update rule');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      if (!rule?.id) return;
      setIsLoading(true);
      await settingsService.deleteMessagingRule(rule.id);
      toast.success('Rule deleted successfully');
      setIsEditing(false);
      await fetchActiveRule();
    } catch (error: unknown) {
      console.error('Delete error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete rule');
    } finally {
      setIsLoading(false);
    }
  };

  const renderContent = () => {
    if (isLoading) return null;
    
    if (!rule && !isEditing) {
      return (
        <div className="text-center py-8">
          <p className="text-sm text-gray-600 mb-2">No messaging rule configured.</p>
          <p className="text-xs text-gray-500">Click the Add Rule button to configure messaging rules.</p>
        </div>
      );
    }

    if (!isEditing && rule) {
      return (
        <div className="space-y-4 mt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">Lead Init Delay</h3>
              <p className="text-lg text-gray-900">{rule.lead_init_delay_h} hours</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">Max Followups</h3>
              <p className="text-lg text-gray-900">{rule.max_followups}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-1">No Response Delay</h3>
              <p className="text-lg text-gray-900">{rule.no_response_delay_h} hours</p>
            </div>
          </div>
          {typeof rule.is_active !== 'undefined' && (
            <div className="mt-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                rule.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {rule.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="space-y-4 mt-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Lead Init Delay (Hours)</label>
            <input
              name="lead_init_delay_h"
              value={rule?.lead_init_delay_h}
              min={0}
              placeholder="Enter delay in hours"
              onChange={handleChange}
              type="number"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Max Followups</label>
            <input
              name="max_followups"
              value={rule?.max_followups}
              min={0}
              placeholder="Enter max followups"
              onChange={handleChange}
              type="number"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">No Response Delay (Hours)</label>
            <input
              name="no_response_delay_h"
              value={rule?.no_response_delay_h}
              min={0}
              placeholder="Enter delay in hours"
              onChange={handleChange}
              type="number"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>

        {isEditing && rule?.id && (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              name="is_active"
              checked={rule?.is_active || false}
              onChange={handleChange}
              className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
            />
            <span className="text-sm text-gray-700">Active</span>
          </div>
        )}

        <div className="flex space-x-3">
          {rule?.id ? (
            <button
              onClick={handleUpdate}
              disabled={isLoading || !isFormValid()}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center"
            >
              <Save className="w-4 h-4 mr-2" /> Update
            </button>
          ) : (
            <button
              onClick={handleCreate}
              disabled={isLoading || !isFormValid()}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center"
            >
              <Save className="w-4 h-4 mr-2" /> Create Rule
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 relative">
      {isLoading && (
        <div className="absolute inset-0 bg-white/60 backdrop-blur-sm flex items-center justify-center rounded-xl">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            <p className="mt-2 text-sm text-gray-600">Processing...</p>
          </div>
        </div>
      )}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <SettingsIcon className="w-5 h-5 text-green-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900">Messaging Rules</h2>
        </div>
        <div className="flex items-center space-x-3">
          {!rule && !isLoading && (
            <button
              onClick={() => {
                setRule({
                  lead_init_delay_h: 0,
                  max_followups: 0,
                  no_response_delay_h: 0,
                  is_active: true
                });
                setIsEditing(true);
              }}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg"
            >
              Add Rule
            </button>
          )}
          {rule?.id && !isLoading && (
            <>
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg flex items-center"
                disabled={isEditing}
              >
                <Edit className="w-4 h-4 mr-2" /> Edit
              </button>
              <button
                onClick={handleDelete}
                disabled={isLoading}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg flex items-center"
              >
                <Trash2 className="w-4 h-4 mr-2" /> Delete
              </button>
            </>
          )}
        </div>
      </div>

      {renderContent()}
    </div>
  );
};

export default MessagingRulesSection;
