import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  FileText,
  Edit,
  Trash2,
  Plus,
  Upload,
  Download,
  Eye,
  AlertCircle,
  Loader2,
  ChevronUp,
  ChevronDown,
  Search
} from 'lucide-react';
import { franchisorsService, Franchisor } from '../../services/franchisorsService';
import { questionsService, type QuestionItem } from '../../services/questionsService';
import { documentsService, type DocumentItem } from '../../services/documentsService';
import toast from 'react-hot-toast';

// Mock interfaces for sections that don't have APIs yet
interface PrequalificationQuestion {
  id: string;
  question: string;
  required: boolean;
  order: number;
  type: 'text' | 'number' | 'boolean' | 'select';
  options?: string[];
}

// Removed unused Document interface - using DocumentItem from documentsService

// Removed QuestionBankItem interface - will be implemented later

// Question Item Component
const QuestionItem = ({
  question,
  onEdit,
  onDelete,
  onMoveUp,
  onMoveDown
}: {
  question: PrequalificationQuestion,
  onEdit: (q: PrequalificationQuestion) => void,
  onDelete: (id: string) => void,
  onMoveUp: (id: string) => void,
  onMoveDown: (id: string) => void
}) => {
  return (
    <div className="flex items-center bg-white p-4 rounded-lg border border-gray-200 mb-3 group">
      <div className="flex flex-col mr-3 text-gray-400">
        <button
          onClick={() => onMoveUp(question.id)}
          className="p-1 hover:text-gray-600"
          title="Move Up"
        >
          <ChevronUp className="w-4 h-4" />
        </button>
        <button
          onClick={() => onMoveDown(question.id)}
          className="p-1 hover:text-gray-600"
          title="Move Down"
        >
          <ChevronDown className="w-4 h-4" />
        </button>
      </div>
      <div className="flex-1">
        <div className="font-medium text-gray-900">{question.question}</div>
        <div className="text-sm text-gray-500 mt-1">
          <span className="capitalize">{question.type}</span>
          {question.required && <span className="ml-2 text-red-500">*Required</span>}
        </div>
      </div>
      <div className="flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          onClick={() => onEdit(question)}
          className="p-1 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50"
        >
          <Edit className="w-4 h-4" />
        </button>
        <button
          onClick={() => onDelete(question.id)}
          className="p-1 text-red-600 hover:text-red-800 rounded-full hover:bg-red-50"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

const FranchisorDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [franchisor, setFranchisor] = useState<Franchisor | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'basic' | 'questions' | 'documents' | 'questionBank' | 'brochure'>('basic');

  // API data states
  const [apiQuestions, setApiQuestions] = useState<QuestionItem[]>([]);
  const [questionsLoading, setQuestionsLoading] = useState(false);
  const [apiDocuments, setApiDocuments] = useState<DocumentItem[]>([]);
  const [documentsLoading, setDocumentsLoading] = useState(false);
  const [questionBankItems, setQuestionBankItems] = useState<any[]>([]);
  const [questionBankLoading, setQuestionBankLoading] = useState(false);

  // Document management states
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [deletingDocumentId, setDeletingDocumentId] = useState<string | null>(null);
  const [deleteModal, setDeleteModal] = useState<{ open: boolean; id: string | null }>({ open: false, id: null });
  const [togglingStatusId, setTogglingStatusId] = useState<string | null>(null);
  const [viewDocument, setViewDocument] = useState<DocumentItem | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [documentsSearch, setDocumentsSearch] = useState('');
  const [documentsFilter, setDocumentsFilter] = useState<'all' | 'active' | 'inactive'>('all');
  
  // Removed mock data - now using API data
  
  // Load franchisor data
  useEffect(() => {
    const loadFranchisor = async () => {
      if (!id) return;

      setLoading(true);
      try {
        const franchisorData = await franchisorsService.getById(id);
        setFranchisor(franchisorData);
      } catch (error) {
        console.error('Error loading franchisor:', error);
        toast.error('Failed to load franchisor details');
      } finally {
        setLoading(false);
      }
    };

    loadFranchisor();
  }, [id]);

  // Reload documents when filter changes
  useEffect(() => {
    if (activeTab === 'documents') {
      loadDocuments();
    }
  }, [documentsFilter]);

  // Load prequalification questions from API
  const loadQuestions = async () => {
    if (!id) return;

    setQuestionsLoading(true);
    try {
      const response = await questionsService.getList({
        page: 1,
        size: 100,
        search: null,
        franchisor_id: id,
        is_active: null,
      });

      // Map API response to QuestionItem format
      const details = (response.data as any).details;
      if (response.success && details && details.items) {
        const items = details.items.map((q: any) => ({
          id: q.id,
          franchisor_id: q.franchisor_id,
          franchisor_name: q.franchisor_name || '',
          industry: q.industry || 'prequalification',
          question_text: q.question_text,
          question_type: q.question_type,
          order: q.order || 0,
          isActive: q.is_active !== undefined ? q.is_active : false,
          created_at: q.created_at,
        }));
        setApiQuestions(items);
      }
    } catch (error) {
      console.error('Error loading questions:', error);
      toast.error('Failed to load prequalification questions');
    } finally {
      setQuestionsLoading(false);
    }
  };

  // Load documents from API with filtering
  const loadDocuments = async () => {
    if (!id) return;

    setDocumentsLoading(true);
    try {
      const filters = {
        skip: 0,
        limit: 100,
        franchisor_id: id,
        search: documentsSearch || undefined,
        is_active: documentsFilter === 'all' ? null : documentsFilter === 'active',
      };

      const response = await documentsService.getList(filters);

      if (response.success && response.data.items) {
        setApiDocuments(response.data.items);
      } else {
        toast.error('Failed to load documents');
      }
    } catch (error) {
      console.error('Error loading documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setDocumentsLoading(false);
    }
  };

  // Load question bank from API
  const loadQuestionBank = async () => {
    setQuestionBankLoading(true);
    try {
      // Filter by current franchisor ID
      const response = await questionsService.getQuestionBank(id);

      if (response.success) {
        setQuestionBankItems(response.data.items);
      }
    } catch (error) {
      console.error('Error loading question bank:', error);
      toast.error('Failed to load question bank');
    } finally {
      setQuestionBankLoading(false);
    }
  };

  // Document management functions
  const handleDeleteDocument = (documentId: string) => {
    setDeleteModal({ open: true, id: documentId });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteModal.id) return;

    try {
      setDeletingDocumentId(deleteModal.id);
      const success = await documentsService.delete(deleteModal.id);

      if (success) {
        setApiDocuments(prev => prev.filter(doc => doc.id !== deleteModal.id));
        toast.success('Document deleted successfully');
      } else {
        toast.error('Failed to delete document');
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error('Failed to delete document');
    } finally {
      setDeletingDocumentId(null);
      setDeleteModal({ open: false, id: null });
    }
  };

  const handleUploadDocument = async (uploadData: {
    file: File;
    name: string;
    description: string | null;
    is_active: boolean;
  }) => {
    if (!id) return;

    try {
      setUploadLoading(true);
      const result = await documentsService.uploadFile({
        file: uploadData.file,
        name: uploadData.name,
        description: uploadData.description,
        franchisor_id: id,
        is_active: uploadData.is_active,
      });

      if (result) {
        toast.success('Document uploaded successfully');
        setShowUploadModal(false);
        // Reload documents
        loadDocuments();
      }
    } catch (error) {
      console.error('Error uploading document:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload document';
      toast.error(errorMessage);
    } finally {
      setUploadLoading(false);
    }
  };

  // Toggle document status
  const handleToggleDocumentStatus = async (documentId: string, currentStatus: boolean) => {
    try {
      setTogglingStatusId(documentId);
      const success = await documentsService.updateStatus(documentId, !currentStatus);

      if (success) {
        setApiDocuments(prev =>
          prev.map(doc =>
            doc.id === documentId
              ? { ...doc, is_active: !currentStatus }
              : doc
          )
        );
        toast.success(`Document ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
      } else {
        toast.error('Failed to update document status');
      }
    } catch (error) {
      console.error('Error toggling document status:', error);
      toast.error('Failed to update document status');
    } finally {
      setTogglingStatusId(null);
    }
  };

  // View document details
  const handleViewDocument = async (document: DocumentItem) => {
    try {
      setViewDocument(document);
      setShowViewModal(true);
    } catch (error) {
      console.error('Error viewing document:', error);
      toast.error('Failed to load document details');
    }
  };

  // Download document
  const handleDownloadDocument = (doc: DocumentItem) => {
    try {
      // Create a temporary link to download the file
      const link = window.document.createElement('a');
      link.href = doc.file_url;
      link.download = doc.name;
      link.target = '_blank';
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);

      toast.success('Download started');
    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error('Failed to download document');
    }
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading franchisor details...</span>
      </div>
    );
  }
  
  if (!franchisor) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Franchisor Not Found</h2>
        <p className="text-gray-600 mb-6">The franchisor you're looking for doesn't exist or has been removed.</p>
        <button
          onClick={() => navigate('/franchisors')}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Back to Franchisors
        </button>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50 pb-12">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/franchisors')}
                className="mr-4 p-2 rounded-full hover:bg-gray-100"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <h1 className="text-2xl font-bold text-gray-900">{franchisor.name}</h1>
              {franchisor.is_active ? (
                <span className="ml-3 px-2.5 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                  Active
                </span>
              ) : (
                <span className="ml-3 px-2.5 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
                  Inactive
                </span>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => navigate(`/franchisors/${franchisor.id}/edit`)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </button>
            </div>
          </div>
          
          {/* Tabs */}
          <div className="mt-6 border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('basic')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'basic'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Basic Information
              </button>
              <button
                onClick={() => setActiveTab('brochure')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'brochure'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Brochure
              </button>
              <button
                onClick={() => {
                  setActiveTab('documents');
                  loadDocuments();
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'documents'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Documents
              </button>
              <button
                onClick={() => {
                  setActiveTab('questions');
                  loadQuestions();
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'questions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Prequalification Questions
              </button>
              <button
                onClick={() => {
                  setActiveTab('questionBank');
                  loadQuestionBank();
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'questionBank'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Question Bank
              </button>
            </nav>
          </div>
        </div>
      </div>
      
      {/* Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        {/* Basic Information Tab */}
        {activeTab === 'basic' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Brand Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Brand ID</label>
                  <div className="text-gray-900">{franchisor.id}</div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Won ID</label>
                  <div className="text-gray-900">{franchisor.franchisor_won_id || '-'}</div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Brand Name</label>
                  <div className="text-gray-900">{franchisor.name}</div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                  <div className="text-gray-900">{franchisor.industry_details?.name || '-'}</div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Region</label>
                  <div className="text-gray-900">{franchisor.region || '-'}</div>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Budget</label>
                  <div className="text-gray-900">${franchisor.budget?.toLocaleString() || '0'}</div>
                </div>
              </div>
              
              <div>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Contact First Name</label>
                  <div className="text-gray-900">{franchisor.contactFirstName || '-'}</div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Contact Last Name</label>
                  <div className="text-gray-900">{franchisor.contactLastName || '-'}</div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <div className="text-gray-900">{franchisor.email || '-'}</div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                  <div className="text-gray-900">{franchisor.phone || '-'}</div>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Created At</label>
                  <div className="text-gray-900">
                    {new Date(franchisor.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Prequalification Questions Tab */}
        {activeTab === 'questions' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Prequalification Questions</h2>
              <button
                onClick={() => navigate(`/franchisors/${id}/edit`)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit Questions
              </button>
            </div>

            {questionsLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading questions...</span>
              </div>
            ) : (
              <>
                <div className="mb-4 text-sm text-gray-500">
                  <p>These questions are shown to leads during the prequalification process.</p>
                </div>

                {apiQuestions.map((question, index) => (
                  <div key={question.id} className="flex items-center bg-gray-50 p-4 rounded-lg border border-gray-200 mb-3">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{question.question_text}</div>
                      <div className="text-sm text-gray-500 mt-1">
                        <span className="capitalize">{question.question_type}</span>
                        <span className="ml-2">Order: {question.order}</span>
                        <span className={`ml-2 ${question.isActive ? 'text-green-600' : 'text-gray-500'}`}>
                          {question.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                    <div className="text-sm text-gray-400">
                      #{index + 1}
                    </div>
                  </div>
                ))}

                {apiQuestions.length === 0 && (
                  <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 mb-1">No questions yet</h3>
                    <p className="text-gray-500 mb-4">Add prequalification questions for this brand</p>
                    <button
                      onClick={() => navigate(`/franchisors/${id}/edit`)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 inline-flex items-center"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add First Question
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        )}
        
        {/* Documents Tab */}
        {activeTab === 'documents' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Documents</h2>
              <button
                onClick={() => setShowUploadModal(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload Document
              </button>
            </div>

            {/* Search and Filter */}
            <div className="mb-6 flex flex-col sm:flex-row gap-4">
              <div className="flex-1 flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search documents..."
                    value={documentsSearch}
                    onChange={(e) => setDocumentsSearch(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        loadDocuments();
                      }
                    }}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <button
                  onClick={loadDocuments}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 flex items-center"
                  title="Search documents"
                >
                  <Search className="w-4 h-4" />
                </button>
              </div>
              <div className="sm:w-48">
                <select
                  value={documentsFilter}
                  onChange={(e) => {
                    setDocumentsFilter(e.target.value as 'all' | 'active' | 'inactive');
                    loadDocuments();
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Documents</option>
                  <option value="active">Active Only</option>
                  <option value="inactive">Inactive Only</option>
                </select>
              </div>
            </div>
            
            {documentsLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading documents...</span>
              </div>
            ) : (
              <>
                {apiDocuments.length > 0 ? (
                  <div className="overflow-hidden border border-gray-200 rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Name
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Type
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Size
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Uploaded
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {apiDocuments.map((doc) => (
                          <tr key={doc.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <FileText className="w-5 h-5 text-gray-400 mr-3" />
                                <div>
                                  <div className="text-sm font-medium text-gray-900">{doc.name}</div>
                                  {doc.description && (
                                    <div className="text-sm text-gray-500">{doc.description}</div>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {doc.file_type}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {doc.file_size}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <button
                                onClick={() => handleToggleDocumentStatus(doc.id, doc.is_active)}
                                disabled={togglingStatusId === doc.id}
                                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full cursor-pointer transition-colors ${
                                  doc.is_active
                                    ? 'bg-green-100 text-green-800 hover:bg-green-200'
                                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                                } ${togglingStatusId === doc.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                                title="Click to toggle status"
                              >
                                {togglingStatusId === doc.id ? (
                                  <Loader2 className="w-3 h-3 animate-spin mr-1" />
                                ) : null}
                                {doc.is_active ? 'Active' : 'Inactive'}
                              </button>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(doc.created_at).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end space-x-2">
                                <button
                                  onClick={() => handleViewDocument(doc)}
                                  className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                                  title="View document details"
                                >
                                  <Eye className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleDownloadDocument(doc)}
                                  className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                                  title="Download document"
                                >
                                  <Download className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleDeleteDocument(doc.id)}
                                  disabled={deletingDocumentId === doc.id}
                                  className="text-red-600 hover:text-red-900 disabled:opacity-50 p-1 rounded hover:bg-red-50"
                                  title="Delete document"
                                >
                                  {deletingDocumentId === doc.id ? (
                                    <Loader2 className="w-4 h-4 animate-spin" />
                                  ) : (
                                    <Trash2 className="w-4 h-4" />
                                  )}
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 mb-1">No documents yet</h3>
                    <p className="text-gray-500 mb-4">Upload documents for this franchisor</p>
                    <button
                      onClick={() => setShowUploadModal(true)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 inline-flex items-center"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Upload First Document
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        )}
        
        {/* Question Bank Tab */}
        {activeTab === 'questionBank' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Question Bank</h2>
            </div>
            
            <div className="mb-4 text-sm text-gray-500">
              <p>Question bank contains reusable questions for this franchisor.</p>
            </div>
            
            {questionBankLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading question bank...</span>
              </div>
            ) : (
              <div className="overflow-hidden border border-gray-200 rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Question Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created At
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {questionBankItems.length > 0 ? (
                      questionBankItems.map((item) => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{item.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              item.is_active
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {item.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(item.created_at).toLocaleDateString()}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={3} className="px-6 py-12 text-center">
                          <div className="text-gray-500">
                            <FileText className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                            <h3 className="text-lg font-medium text-gray-900 mb-1">No Questions Available</h3>
                            <p className="text-gray-500">No questions found in the question bank.</p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Brochure Tab */}
        {activeTab === 'brochure' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Brand Brochure</h2>
            </div>

            {franchisor?.brochure_url ? (
              <div className="space-y-6">
                {/* Brochure Preview */}
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="text-sm font-medium text-gray-900">Current Brochure</h3>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center justify-center bg-gray-100 rounded-lg h-96">
                      <iframe
                        src={franchisor.brochure_url}
                        className="w-full h-full rounded-lg"
                        title="Brand Brochure Preview"
                      />
                    </div>
                    <div className="mt-4 flex justify-center space-x-3">
                      <a
                        href={franchisor.brochure_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center"
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        View Full Size
                      </a>
                      <a
                        href={franchisor.brochure_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        download
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Download
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-500">
                  <FileText className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-1">No Brochure Available</h3>
                  <p className="text-gray-500">No brochure has been uploaded for this brand yet.</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Upload Document Modal */}
      {showUploadModal && (
        <DocumentUploadModal
          onClose={() => setShowUploadModal(false)}
          onUpload={handleUploadDocument}
          loading={uploadLoading}
        />
      )}

      {/* Delete Confirmation Modal */}
      {deleteModal.open && (
        <DeleteConfirmModal
          open={deleteModal.open}
          title="Delete Document"
          message="Are you sure you want to delete this document? This action cannot be undone."
          onCancel={() => setDeleteModal({ open: false, id: null })}
          onConfirm={handleDeleteConfirm}
          loading={!!deletingDocumentId}
        />
      )}

      {/* Document View Modal */}
      {showViewModal && viewDocument && (
        <DocumentViewModal
          document={viewDocument}
          onClose={() => {
            setShowViewModal(false);
            setViewDocument(null);
          }}
        />
      )}
    </div>
  );
};

// Document Upload Modal Component
interface DocumentUploadModalProps {
  onClose: () => void;
  onUpload: (data: {
    file: File;
    name: string;
    description: string | null;
    is_active: boolean;
  }) => void;
  loading: boolean;
}

const DocumentUploadModal: React.FC<DocumentUploadModalProps> = ({
  onClose,
  onUpload,
  loading,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true,
  });
  const [file, setFile] = useState<File | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      if (!formData.name) {
        // Auto-fill name from filename without extension
        const nameWithoutExt = selectedFile.name.replace(/\.[^/.]+$/, '');
        setFormData(prev => ({ ...prev, name: nameWithoutExt }));
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;

    onUpload({
      file,
      name: formData.name,
      description: formData.description || null,
      is_active: formData.is_active,
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Upload Document</h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Document Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload File *
            </label>
            <input
              type="file"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              onChange={handleFileChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
              className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <label htmlFor="is_active" className="ml-2 text-sm text-gray-700">
              Mark as active
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!file || !formData.name || loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Uploading...</span>
                </>
              ) : (
                <span>Upload</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Delete Confirmation Modal Component
interface DeleteConfirmModalProps {
  open: boolean;
  title: string;
  message: string;
  onCancel: () => void;
  onConfirm: () => void;
  loading: boolean;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  open,
  title,
  message,
  onCancel,
  onConfirm,
  loading,
}) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
        <div className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">{title}</h2>
          <p className="text-gray-600 mb-6">{message}</p>

          <div className="flex justify-end space-x-3">
            <button
              onClick={onCancel}
              disabled={loading}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={onConfirm}
              disabled={loading}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Deleting...</span>
                </>
              ) : (
                <span>Delete</span>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Document View Modal Component
interface DocumentViewModalProps {
  document: DocumentItem;
  onClose: () => void;
}

const DocumentViewModal: React.FC<DocumentViewModalProps> = ({ document, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">Document Details</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
        </div>

        <div className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <p className="text-gray-900">{document.name}</p>
          </div>

          {document.description && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <p className="text-gray-900">{document.description}</p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">File Type</label>
              <p className="text-gray-900">{document.file_type}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">File Size</label>
              <p className="text-gray-900">{document.file_size}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                document.is_active
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {document.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Uploaded</label>
              <p className="text-gray-900">{new Date(document.created_at).toLocaleDateString()}</p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">File Path</label>
            <p className="text-gray-900 break-all">{document.file_path}</p>
          </div>

          {/* Document Preview/Link */}
          <div className="pt-4 border-t border-gray-200">
            <div className="flex space-x-3">
              <button
                onClick={() => window.open(document.file_url, '_blank')}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2"
              >
                <Eye className="w-4 h-4" />
                <span>View Document</span>
              </button>
              <button
                onClick={() => {
                  const link = window.document.createElement('a');
                  link.href = document.file_url;
                  link.download = document.name;
                  link.target = '_blank';
                  window.document.body.appendChild(link);
                  link.click();
                  window.document.body.removeChild(link);
                }}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center justify-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Download</span>
              </button>
            </div>
          </div>
        </div>

        <div className="p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="w-full px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default FranchisorDetails;
