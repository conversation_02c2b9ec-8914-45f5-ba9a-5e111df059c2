import React, { useState, useEffect, useCallback } from 'react';
import {
  MessageSquare,
  Trash2,
  Loader2,
  Calendar,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { conversationService, ConversationMessage } from '../../services/conversationService';
import toast from 'react-hot-toast';
import Pagination from '../common/Pagination';
import DeleteConfirmModal from '../common/DeleteConfirmModal';

interface SmsConversationProps {
  leadId: string;
  leadName?: string;
}

const SmsConversation: React.FC<SmsConversationProps> = ({ leadId, leadName }) => {
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletingMessageId, setDeletingMessageId] = useState<string | null>(null);
  const [deleteModal, setDeleteModal] = useState<{ open: boolean; messageId: string | null }>({
    open: false,
    messageId: null
  });

  // Pagination
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [totalItems, setTotalItems] = useState(0);

  // Load conversations for this specific lead
  const loadConversations = useCallback(async () => {
    try {
      setLoading(true);
      const response = await conversationService.getMessagesByLeadId({
        lead_id: leadId,
        page: pagination.pageIndex + 1,
        per_page: pagination.pageSize,
        order: 'desc'
      });

      if (response.success) {
        setMessages(response.data?.items || []);
        setTotalItems(response.pagination?.total_items || 0);
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
      toast.error('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  }, [leadId, pagination.pageIndex, pagination.pageSize]);

  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  // Handle delete message
  const handleDeleteMessage = async (messageId: string) => {
    setDeleteModal({ open: true, messageId });
  };

  const confirmDeleteMessage = async () => {
    if (!deleteModal.messageId) return;

    try {
      setDeletingMessageId(deleteModal.messageId);
      await conversationService.deleteMessage(deleteModal.messageId);
      toast.success('Message deleted successfully');
      
      // Reload conversations
      await loadConversations();
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error('Failed to delete message');
    } finally {
      setDeletingMessageId(null);
      setDeleteModal({ open: false, messageId: null });
    }
  };

  if (loading && (!messages || messages.length === 0)) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading SMS conversations...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">SMS Conversations</h2>
          {leadName && (
            <p className="text-gray-600 mt-1">
              Conversations with {leadName}
            </p>
          )}
        </div>
        <button
          onClick={loadConversations}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Refresh</span>
        </button>
      </div>

      {/* Messages List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading messages...</span>
          </div>
        ) : !messages || messages.length === 0 ? (
          <div className="text-center py-12">
            <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <div className="text-gray-500 text-lg">No SMS messages found</div>
            <div className="text-gray-400 text-sm mt-1">
              No SMS conversations have been recorded for this lead yet.
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {(messages || []).map((message) => {
              const isFromLead = message.sender === 'lead';
              return (
                <div key={message.id} className={`p-4 flex ${isFromLead ? 'justify-start' : 'justify-end'}`}>
                  <div className={`max-w-md lg:max-w-lg xl:max-w-2xl ${isFromLead ? 'order-1' : 'order-2'}`}>
                    {/* Message bubble */}
                    <div className={`rounded-lg p-4 ${
                      isFromLead
                        ? 'bg-gray-100 text-gray-900'
                        : 'bg-blue-600 text-white'
                    }`}>
                      <div className="text-sm whitespace-pre-wrap">
                        {message.message}
                      </div>
                    </div>

                    {/* Message info */}
                    <div className={`mt-1 flex items-center space-x-2 text-xs text-gray-500 ${
                      isFromLead ? 'justify-start' : 'justify-end'
                    }`}>
                      <span className={`px-2 py-1 rounded-full ${
                        isFromLead
                          ? 'bg-gray-200 text-gray-700'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {isFromLead ? 'Lead' : 'System'}
                      </span>
                      <span>{new Date(message.created_at).toLocaleString()}</span>

                      {/* Delete button */}
                      <button
                        onClick={() => handleDeleteMessage(message.id)}
                        disabled={deletingMessageId === message.id}
                        className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                        title="Delete message"
                      >
                        {deletingMessageId === message.id ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <Trash2 className="w-3 h-3" />
                        )}
                      </button>
                    </div>

                    {message.franchisor_name && (
                      <div className={`mt-1 text-xs text-gray-500 ${
                        isFromLead ? 'text-left' : 'text-right'
                      }`}>
                        Franchisor: {message.franchisor_name}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Pagination */}
        {!loading && messages && messages.length > 0 && (
          <div className="border-t border-gray-200 px-4 py-3">
            <Pagination
              pageIndex={pagination.pageIndex}
              pageSize={pagination.pageSize}
              totalCount={totalItems}
              pageCount={Math.ceil(totalItems / pagination.pageSize)}
              onPageChange={(page) => setPagination(prev => ({ ...prev, pageIndex: page }))}
              onPrevious={() => setPagination(prev => ({ ...prev, pageIndex: Math.max(0, prev.pageIndex - 1) }))}
              onNext={() => setPagination(prev => ({ ...prev, pageIndex: prev.pageIndex + 1 }))}
              canPreviousPage={pagination.pageIndex > 0}
              canNextPage={pagination.pageIndex < Math.ceil(totalItems / pagination.pageSize) - 1}
            />
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        open={deleteModal.open}
        title="Delete Message"
        message="Are you sure you want to delete this SMS message? This action cannot be undone."
        onCancel={() => setDeleteModal({ open: false, messageId: null })}
        onConfirm={confirmDeleteMessage}
        loading={!!deletingMessageId}
      />
    </div>
  );
};

export default SmsConversation;
