import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';
import { leadService, Lead } from '../../services/leadService';
import { questionsService, FranchisorDropdown } from '../../services/questionsService';
import ComingSoon from '../common/ComingSoon';
import SmsConversation from '../sms/SmsConversation';

const LeadDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [lead, setLead] = useState<Lead | null>(null);
  const [loading, setLoading] = useState(true);
  const [franchisors, setFranchisors] = useState<FranchisorDropdown[]>([]);
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    const loadData = async () => {
      if (!id) {
        console.error('No lead ID provided');
        return;
      }

      try {
        setLoading(true);
        const [leadRes, franchisorsRes] = await Promise.all([
          leadService.getLeadById(id),
          questionsService.getFranchisorsDropdown()
        ]);

        if (leadRes?.success) {
          setLead(leadRes.data);
        } else {
          console.error('Failed to load lead:', leadRes);
          toast.error('Lead not found');
          navigate('/leads');
          return;
        }

        if (franchisorsRes?.success && franchisorsRes.data?.details?.franchisors) {
          setFranchisors(franchisorsRes.data.details.franchisors);
        } else {
          console.error('Invalid franchisors response:', franchisorsRes);
          setFranchisors([]);
        }
      } catch (error) {
        console.error('Error loading lead details:', error);
        toast.error('Failed to load lead details');
        navigate('/leads');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id]);

  const getFranchiseName = (franchiseId: string) => {
    const franchise = franchisors.find(f => f.id === franchiseId);
    return franchise ? franchise.name : 'Unknown Franchisor';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading lead details...</div>
        </div>
      </div>
    );
  }

  if (!lead) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500">Lead not found</div>
        <button
          onClick={() => navigate('/leads')}
          className="mt-4 text-blue-600 hover:text-blue-800"
        >
          Back to Leads
        </button>
      </div>
    );
  }

  const tabs = [
    { id: 'basic', name: 'Basic Information' },
    { id: 'buyer', name: 'Franchise Buyer Info' },
    { id: 'sms', name: 'SMS Listing' },
    { id: 'meetings', name: 'Meeting Bookings' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/leads')}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {`${lead.first_name || ''} ${lead.last_name || ''}`.trim() || 'Lead Details'}
                </h1>
                <p className="text-sm text-gray-500">
                  Lead ID: {lead.id}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                lead.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {lead.is_active ? 'Active' : 'Inactive'}
              </span>
              <button
                onClick={() => navigate(`/leads/${id}/edit`)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <Edit className="w-4 h-4" />
                <span>Edit Lead</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <nav className="flex space-x-8" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        {/* Basic Information Tab */}
        {activeTab === 'basic' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Lead ID</label>
                  <div className="text-gray-900">{lead.id}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Zoho Lead ID</label>
                  <div className="text-gray-900">{lead.zoho_lead_id || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                  <div className="text-gray-900">{lead.first_name || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                  <div className="text-gray-900">{lead.last_name || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <div className="text-gray-900">{lead.email || '-'}</div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                  <div className="text-gray-900">{lead.phone || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Mobile</label>
                  <div className="text-gray-900">{lead.mobile || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                  <div className="text-gray-900">{lead.location || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                  <div className="text-gray-900">{lead.postal_code || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Lead Source</label>
                  <div className="text-gray-900">{lead.lead_source_name || '-'}</div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8 pt-6 border-t border-gray-200">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Lead Status</label>
                <div className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: lead.lead_status_colour || '#gray' }}
                  />
                  <span className="text-gray-900">{lead.lead_status_name || '-'}</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Brand Preference</label>
                <div className="text-gray-900">{getFranchiseName(lead.brand_preference) || '-'}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Budget Preference</label>
                <div className="text-gray-900">{lead.budget_preference || '-'}</div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Created At</label>
                <div className="text-gray-900">
                  {new Date(lead.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Franchise Buyer Info Tab */}
        {activeTab === 'buyer' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Franchise Buyer Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Franchise Interested In</label>
                  <div className="text-gray-900">{getFranchiseName(lead.brand_preference) || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Looking for Business Opportunity Since</label>
                  <div className="text-gray-900">{lead.looking_for_business_opportunity_since || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Skills</label>
                  <div className="text-gray-900">{lead.skills || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Looking to be Owner Operator?</label>
                  <div className="text-gray-900">{lead.looking_to_be_owner_operator || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">When Looking to Start</label>
                  <div className="text-gray-900">{lead.when_looking_to_start || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Ethnic Background</label>
                  <div className="text-gray-900">{lead.ethnic_background || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Funds to Invest</label>
                  <div className="text-gray-900">{lead.funds_to_invest || '-'}</div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Work Background</label>
                  <div className="text-gray-900">{lead.work_background || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Motivation to Enquire</label>
                  <div className="text-gray-900">{lead.motivation_to_enquire || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Funds Available</label>
                  <div className="text-gray-900">{lead.funds_available || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Motivation</label>
                  <div className="text-gray-900">{lead.motivation || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Have Run Business Before?</label>
                  <div className="text-gray-900">{lead.have_run_business_before || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Have Mortgage?</label>
                  <div className="text-gray-900">{lead.have_mortgage || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">High Net Worth</label>
                  <div className="text-gray-900">{lead.high_net_worth || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">EOI/NDA Link</label>
                  <div className="text-gray-900">
                    {lead.eoi_nda_link ? (
                      <a href={lead.eoi_nda_link} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                        View Link
                      </a>
                    ) : '-'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* SMS Listing Tab */}
        {activeTab === 'sms' && (
          <ComingSoon
            title="SMS Listing"
            description="SMS listing functionality is currently under development and will be available soon."
            // features={[
            //   "Send SMS campaigns to leads",
            //   "Track SMS delivery status",
            //   "Automated SMS sequences",
            //   "SMS templates management"
            // ]}
          />
        )}

        {/* Meeting Bookings Tab */}
        {activeTab === 'meetings' && (
          <ComingSoon
            title="Meeting Bookings"
            description="Meeting booking functionality is currently under development and will be available soon."
            // features={[
            //   "Schedule meetings with leads",
            //   "Calendar integration",
            //   "Meeting reminders",
            //   "Video call integration"
            // ]}
          />
        )}
      </div>
    </div>
  );
};

export default LeadDetails;
