// NOTE: In this codebase, 'franchise' and 'brand' refer to the same thing, and so do 'category' and 'industry'.
// The UI displays 'Brand' and 'Industry', but variable/function names may still use 'franchise' and 'category' for legacy reasons..
import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  Plus,
  Search,
  Trash2,
  Download,
  Eye,
  User,
  Info,
  FileText,
  Loader2,
  FileType,
  File as FileIcon,
  Folder,
  Calendar,
  Link as LinkIcon,
  Edit2,
  Filter,
  Upload,
  ArrowUp,
  ArrowDown,
  ArrowUpDown,
} from "lucide-react";
import toast from "react-hot-toast";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
  SortingState,
} from "@tanstack/react-table";
import {
  documentsService,
  DocumentItem,
  DocumentFilters,
} from "../../services/documentsService";
import { allowedTypes, getFileTypeLabel } from "../../utils/fileTypes";
import DeleteConfirmModal from "../common/DeleteConfirmModal";
import Select from "../common/Select";
import { FranchisorDropdown, questionsService } from "../../services/questionsService";
import Pagination from "../common/Pagination";

const Documents: React.FC = () => {
  const isRequestInProgress = useRef(false);
  const [documents, setDocuments] = useState<DocumentItem[]>([]);
  const [loading, setLoading] = useState({
    table: false,
    page: true,
    actions: false,
  });
  const [deletingDocument, setDeletingDocument] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [totalItems, setTotalItems] = useState(0);
  const [franchisors, setFranchisors] = useState<FranchisorDropdown[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [franchiseFilter, setFranchiseFilter] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [viewModal, setViewModal] = useState<{
    open: boolean;
    documentId: string | null;
  }>({ open: false, documentId: null });
  const [viewModalLoading, setViewModalLoading] = useState(false);
  const [viewDocument, setViewDocument] = useState<DocumentItem | null>(null);
  const [togglingStatus, setTogglingStatus] = useState<string | null>(null);
  const [isActiveFilter, setIsActiveFilter] = useState<boolean | null>(null);
  const [fileTypeFilter, setFileTypeFilter] = useState("");
  const [showBulkUploadModal, setShowBulkUploadModal] = useState(false);
  const [bulkUploading, setBulkUploading] = useState(false);
  const [deleteModal, setDeleteModal] = useState<{ open: boolean; id: string | null }>({ open: false, id: null });
  const [sorting, setSorting] = useState<SortingState>([]);
  

  // Event handlers
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    },
    []
  );


  const clearFilters = useCallback(() => {
    setSearchTerm("");
    setFranchiseFilter("");
    setFileTypeFilter("");
    setIsActiveFilter(null);
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    setDebouncedSearchTerm("");
  }, []);

  // Initial data load
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading((prev) => ({ ...prev, page: true, table: true }));

        // Load documents first
        const documentsResponse = await documentsService.getList({
          skip: 0,
          limit: pagination.pageSize,
        });

        if (documentsResponse.success) {
          setDocuments(documentsResponse.data.items);
          setTotalItems(documentsResponse.data.total_count);
          setLoading((prev) => ({ ...prev, page: false }));
        }

        // Load brands for filter, show loading only on filter
      } catch (error) {
        console.error("Error loading initial data:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to load data";
        toast.error(errorMessage);
      } finally {
        setLoading((prev) => ({ ...prev, page: false, table: false }));
      }
    };
    loadInitialData();
  }, []);

  useEffect(() => {
    const loadFranchisorsForFilter = async () => {
      setLoading((prev) => ({ ...prev, actions: true }));
      try {
        const franchisorsResponse = await questionsService.getFranchisorsDropdown();
        if (franchisorsResponse.success && franchisorsResponse.data?.details?.franchisors) {
          setFranchisors(franchisorsResponse.data.details.franchisors);
        }
      } catch (error) {
        if (
          typeof error === "object" &&
          error !== null &&
          ("name" in error || "code" in error)
        ) {
          if (
            (error as { name?: string }).name === "AbortError" ||
            (error as { code?: string }).code === "ERR_CANCELED" ||
            (error as { code?: string }).code === "ECONNABORTED"
          ) {
            return;
          }
        }
        console.error("Error loading brands:", error);
        toast.error("Failed to load brands");
      } finally {
        setLoading((prev) => ({ ...prev, actions: false }));
      }
    };

    // Load brands for filter on initial mount
    loadFranchisorsForFilter();
  }, []);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Load documents with filters and pagination
  const loadDocuments = useCallback(async () => {
    if (isRequestInProgress.current) return;

    try {
      isRequestInProgress.current = true;
      setLoading((prev) => ({ ...prev, table: true }));

      const sort = sorting[0];

      const filters: DocumentFilters = {
        skip: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        search: debouncedSearchTerm || undefined,
        franchisor_id: franchiseFilter || undefined,
        file_type: fileTypeFilter || undefined,
        is_active: isActiveFilter,
        sortBy: sort?.id || null,       // Which column to sort by
        sortOrder: sort?.desc ? 'desc' : 'asc'
      };

      const response = await documentsService.getList(filters);

      if (response.success) {
        setDocuments(response.data.items);
        setTotalItems(response.data.total_count);

        const totalPages = Math.ceil(
          response.data.total_count / pagination.pageSize
        );
        if (pagination.pageIndex >= totalPages && totalPages > 0) {
          setPagination((prev) => ({ ...prev, pageIndex: totalPages - 1 }));
        }
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load data");
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    } finally {
      setLoading((prev) => ({ ...prev, table: false, page: false }));
      isRequestInProgress.current = false;
    }
  }, [
    pagination.pageIndex,
    pagination.pageSize,
    debouncedSearchTerm,
    franchiseFilter,
    fileTypeFilter,
    isActiveFilter,
    sorting
  ]);

  // Load filtered/paginated documents
  useEffect(() => {
    if (!loading.page) {
      // Don't load while initial load is in progress
      loadDocuments();
    }
  }, [
    debouncedSearchTerm,
    franchiseFilter,
    pagination.pageIndex,
    pagination.pageSize,
    loading.page,
    fileTypeFilter,
    isActiveFilter,
    sorting
  ]);

  // Get franchise name by ID
  const getFranchiseName = (franchiseId: string) => {
    if (loading.actions) {
      return "Loading...";
    }
    const franchise = franchisors.find((f) => f.id === franchiseId);
    return franchise ? franchise.name : "Unknown Brand";
  };

  const handleDelete = (id: string) => {
    setDeleteModal({ open: true, id });
  };

  // Handle document deletion
  const handleDeleteConfirm = async () => {
    if (!deleteModal.id) return;

    try {
      setDeletingDocument(deleteModal.id);
      const success = await documentsService.delete(deleteModal.id);

      if (success) {
        setDocuments((prev) => prev.filter((doc) => doc.id !== deleteModal.id));
        toast.success("Document deleted successfully");
        loadDocuments();
      }
    } catch (error) {
      console.error("Failed to delete document:", error);
      toast.error("Failed to delete document");
    } finally {
      setDeletingDocument(null);
      setDeleteModal({ open: false, id: null });
    }
  };

  // Handle document view
  const handleView = useCallback(async (document: DocumentItem) => {
    setViewModal({ open: true, documentId: document.id });
    setViewModalLoading(true);
    try {
      const doc = await documentsService.getById(document.id);
      setViewDocument(doc);
    } catch (error) {
      console.error("Failed to load document details", error);
      toast.error("Failed to load document details");
      setViewModal({ open: false, documentId: null });
    } finally {
      setViewModalLoading(false);
    }
  }, []);

  // Toggle status handler
  const handleToggleStatus = useCallback(
    async (id: string, currentStatus: boolean) => {
      try {
        setTogglingStatus(id);
        const success = await documentsService.updateStatus(id, !currentStatus);
        if (success) {
          setDocuments((prev) =>
            prev.map((doc) =>
              doc.id === id ? { ...doc, is_active: !currentStatus } : doc
            )
          );
          toast.success(
            `Document ${
              !currentStatus ? "activated" : "deactivated"
            } successfully!`
          );
        } else {
          toast.error("Failed to update document status. Please try again.");
        }
      } catch (error) {
        console.error("Failed to toggle document status:", error);
        toast.error("Failed to update document status. Please try again.");
      } finally {
        setTogglingStatus(null);
      }
    },
    []
  );

  const handleDownload = async (url: string, fileName: string) => {
    const response = await fetch(url);
    const blob = await response.blob();

    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    link.click();

    URL.revokeObjectURL(link.href);
  };

  // Define table columns
  const columns = useMemo<ColumnDef<DocumentItem, unknown>[]>(
    () => [
      {
        accessorKey: "name",
        header: "Document Name",
        enableSorting: true,
        cell: (info) => (
          <div className="flex items-center space-x-3">
            <FileText className="w-5 h-5 text-gray-400" />
            <div>
              <div className="font-medium text-gray-900">
                {info.getValue() as string || "Untitled Document"}
              </div>
            </div>
          </div>
        ),
      },
      {
        accessorKey: "file_type",
        header: "Type",
        cell: (info) => (
          <span className="text-gray-700 uppercase">
            {getFileTypeLabel(info.getValue() as string) || "Unknown Type"}
          </span>
        ),
      },
      {
        accessorKey: "franchisor_id",
        header: "Franchisor",
        cell: (info) => (
          <span className="text-gray-700">
            {getFranchiseName(info.getValue() as string || "")}
          </span>
        ),
      },
      {
        accessorKey: "created_at",
        header: "Upload Date",
        enableSorting: true,
        cell: (info) => (
          <span className="text-gray-700">
            {new Date(info.getValue() as string)?.toLocaleDateString() || 'N/A'}
          </span>
        ),
      },
      {
        accessorKey: "is_active",
        header: "Status",
        cell: (info) => {
          const isActive = info.getValue() as boolean;
          const id = info.row.original.id;
          const isToggling = togglingStatus === id;
          return (
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handleToggleStatus(id, isActive)}
                disabled={isToggling}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isActive ? "bg-blue-600" : "bg-gray-200"
                } ${
                  isToggling
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer"
                }`}
                title={isActive ? "Deactivate" : "Activate"}
              >
                {isToggling ? (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Loader2
                      className={`w-4 h-4 animate-spin ${
                        isActive ? "text-white" : "text-gray-900"
                      }`}
                    />
                  </div>
                ) : (
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      isActive ? "translate-x-6" : "translate-x-1"
                    }`}
                  />
                )}
              </button>
            </div>
          );
        },
      },
      {
        id: "actions",
        header: "Actions",
        cell: (info) => (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleView(info.row.original)}
              className="text-blue-600 hover:text-blue-800 p-1"
              title="View Document"
            >
              <Eye className="w-4 h-4" />
            </button>
            <button
              onClick={() =>
                handleDownload(
                  info.row.original.file_url,
                  info.row.original.name
                )
              }
              className="text-green-600 hover:text-green-800 p-1"
              title="Download"
            >
              <Download className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleDelete(info.row.original.id)}
              disabled={deletingDocument === info.row.original.id}
              className="text-red-600 hover:text-red-800 p-1 disabled:opacity-50"
              title="Delete"
            >
              {deletingDocument === info.row.original.id ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4" />
              )}
            </button>
          </div>
        ),
      },
    ],
    [
      getFranchiseName,
      handleView,
      handleDelete,
      handleToggleStatus,
      togglingStatus,
      deletingDocument,
    ]
  );

  // Setup table
  const table = useReactTable({
    data: documents,
    columns,
    pageCount: Math.ceil(totalItems / pagination.pageSize),
    state: {
      pagination: {
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
      },
      sorting,
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
    enableSortingRemoval: false,
  });

  const isFilterActive = searchTerm.trim() !== "" || isActiveFilter !== null || franchiseFilter !== "" || fileTypeFilter !== "";

  // Show loading state for initial data load
  if (loading.page && documents.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading documents...</div>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Document Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage and organize all brands-related documents
          </p>
        </div>
        <div className="flex space-x-3">
          {/* <button
            onClick={() => setShowBulkUploadModal(true)}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
            type="button"
            disabled={bulkUploading}
          >
            <Upload className="w-4 h-4" />
            <span>{bulkUploading ? "Uploading..." : "Bulk Upload"}</span>
          </button> */}
          <button
            onClick={() => setShowModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Upload Document</span>
          </button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col gap-3 md:flex-row md:items-center md:gap-4 flex-wrap">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search documents..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent "
            />
          </div>
          <div className="w-full md:w-auto flex-1 min-w-[160px]">
            <Select
              value={franchiseFilter || "all"}
              onChange={(value) => {
                setFranchiseFilter(value === "all" ? "" : value);
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              placeholder="Select Franchisor"
              options={[
                { value: "all", label: "All Franchisors" },
                ...franchisors.map((brand) => ({
                  value: brand.id,
                  label: brand.name,
                }))
              ]}
            />
          </div>

          {/* Status Filter */}
          <div className="w-full md:w-auto flex-1 min-w-[120px]">
            <Select
              value={isActiveFilter === null ? "" : isActiveFilter.toString()}
              onChange={(value) => {
                setIsActiveFilter(
                  value === "" ? null : value === "true"
                );
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              placeholder="Select Status"
              options={[
                { value: "", label: "All Status" },
                { value: "true", label: "Active" },
                { value: "false", label: "Inactive" }
              ]}
              className="bg-gray-50"
            />
          </div>

          {/* File Type Filter */}
          <div className="w-full md:w-auto flex-1 min-w-[120px]">
            <Select
              value={fileTypeFilter}
              onChange={(value) => {
                setFileTypeFilter(value);
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              placeholder="Select File Type"
              options={[
                { value: "", label: "All File Types" },
                { value: "application/pdf", label: "PDF" },
                { value: "application/msword", label: "DOC" },
                { value: "application/vnd.openxmlformats-officedocument.wordprocessingml.document", label: "DOCX" },
                { value: "image/jpeg", label: "JPEG" },
                { value: "image/png", label: "PNG" }
              ]}
              className="bg-gray-50"
            />
          </div>

          {/* Clear Filters Button */}
          <div className="w-full md:w-auto flex-none">
            <button
              onClick={clearFilters}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 w-full md:w-auto justify-center relative"
            >
              <Filter className="w-4 h-4" />
              <span>Clear Filters</span>

              {isFilterActive && (
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Documents List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 mt-6">
        {loading.table ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading documents...</span>
          </div>
        ) : documents.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg">No documents found</div>
            <div className="text-gray-400 text-sm mt-1">
              Try adjusting your search or filters
            </div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => {
                      const isSortable =
                        header.column.id === "name" ||
                        header.column.id === "created_at";
                      return (
                        <th
                          key={header.id}
                          onClick={isSortable
                              ? header.column.getToggleSortingHandler()
                              : undefined
                          }
                          className={`text-left py-4 px-6 font-medium text-gray-900 ${
                            isSortable
                              ? "cursor-pointer select-none"
                              : ""
                          }`}
                        >
                          <div className="flex items-center gap-1 space-x-1">
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                              {isSortable && (
                              <>
                                {header.column.getIsSorted() === "asc" ? (
                                  <ArrowUp className="w-4 h-4 text-blue-600" />
                                ) : header.column.getIsSorted() === "desc" ? (
                                  <ArrowDown className="w-4 h-4 text-blue-600" />
                                ) : (
                                  <ArrowUpDown className="w-4 h-4 text-gray-400" />
                                )}
                              </>
                            )}
                              </div>
                        </th>
                      )
                    })}
                    </tr>
                  ))}
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {table.getRowModel().rows.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50">
                      {row.getVisibleCells().map((cell) => (
                        <td key={cell.id} className="py-4 px-6">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <Pagination
              pageIndex={pagination.pageIndex}
              pageSize={pagination.pageSize}
              totalCount={totalItems}
              pageCount={table.getPageCount()}
              onPageChange={(page) => setPagination((prev) => ({ ...prev, pageIndex: page }))}
              onPrevious={() => table.previousPage()}
              onNext={() => table.nextPage()}
              canPreviousPage={table.getCanPreviousPage()}
              canNextPage={table.getCanNextPage()}
            />
          </>
        )}
      </div>

      {/* Upload Document Modal */}
      {showModal && (
        <DocumentUploadModal
          franchisors={franchisors}
          onClose={() => setShowModal(false)}
          loading={loading.actions}
          onUpload={async (uploadInput) => {
            try {
              setLoading((prev) => ({ ...prev, actions: true }));
              const result = await documentsService.uploadFile({
                file: uploadInput.file,
                name: uploadInput.name,
                franchisor_id: uploadInput.franchisor_id,
                description: uploadInput.description || null,
                is_active: uploadInput.is_active,
              });

              if (result) {
                toast.success("Document uploaded successfully");
                // Reset to first page
                setPagination({ pageIndex: 0, pageSize: 10 });
                // Close modal before refreshing list to avoid UI jank
                setShowModal(false);
                // Wait for modal to close before refreshing
                setTimeout(() => {
                  loadDocuments();
                }, 100);
              }
            } catch (error) {
              console.error("Failed to upload document:", error);
              const errorMessage =
                error instanceof Error
                  ? error.message
                  : "Failed to upload document";
              toast.error(errorMessage);
            } finally {
              setLoading((prev) => ({ ...prev, actions: false }));
            }
          }}
        />
      )}

      {/* Bulk Upload Modal */}
      {showBulkUploadModal && (
        <BulkUploadModal
          franchisors={franchisors}
          onClose={() => setShowBulkUploadModal(false)}
          loading={bulkUploading}
          onUpload={async (files, franchisor_id, is_active) => {
            setBulkUploading(true);
            try {
              // files is now string[] (base64), pass as needed to documentsService.bulkUpload
              const res = await documentsService.bulkUpload(
                files, // string[]
                franchisor_id,
                is_active
              );
              toast.success(
                res.message?.description || "Bulk upload completed!"
              );
              setShowBulkUploadModal(false);
              setPagination({ pageIndex: 0, pageSize: 10 });
              setTimeout(() => {
                loadDocuments();
              }, 100);
            } catch (error) {
              console.error("Error Uploading Bulk files:", error);
              toast.error("Bulk upload failed.");
            } finally {
              setBulkUploading(false);
            }
          }}
        />
      )}

      {/* View Document Modal */}
      {viewModal.open && (
        <DocumentViewModal
          loading={viewModalLoading}
          document={viewDocument}
          franchisors={franchisors}
          onClose={() => {
            setViewModal({ open: false, documentId: null });
            setViewDocument(null);
          }}
        />
      )}
      <DeleteConfirmModal
        open={deleteModal.open}
        title="Delete Document"
        message="Are you sure you want to delete this document? This action cannot be undone."
        onCancel={() => setDeleteModal({ open: false, id: null })}
        onConfirm={handleDeleteConfirm}
        loading={!!deletingDocument}
      />
    </div>
  );
};

interface UploadModalProps {
  franchisors: FranchisorDropdown[];
  onClose: () => void;
  onUpload: (data: {
    file: File;
    name: string;
    franchisor_id: string;
    description: string | null;
    is_active: boolean;
  }) => void;
  loading: boolean;
}

const DocumentUploadModal: React.FC<UploadModalProps> = ({
  franchisors,
  onClose,
  onUpload,
  loading,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    franchisor_id: "",
    description: "",
    is_active: true,
  });
  const [file, setFile] = useState<File | null>(null);

  const stripExtension = (filename: string) => {
    return filename.replace(/\.[^/.]+$/, ""); // Removes extension
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;

    onUpload({
      file,
      name: stripExtension(formData.name) || stripExtension(file.name),
      franchisor_id: formData.franchisor_id,
      description: formData.description,
      is_active: formData.is_active,
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      if (!allowedTypes.includes(selectedFile.type)) {
        e.target.value = "";
        setFile(null);
        toast.error("Invalid file type");
        return;
      }
      setFile(selectedFile);

      // Auto-fill the name field with the file name if it's empty
      if (!formData.name) {
        setFormData({ ...formData, name: stripExtension(selectedFile.name) });
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Upload Document
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Franchisor *
            </label>
            <Select
              value={formData.franchisor_id}
              onChange={(val) =>
                setFormData({ ...formData, franchisor_id: val })
              }
              placeholder="Select a Franchisor"
              disabled={loading}
              options={[
                ...franchisors.map((brand) => ({
                  value: brand.id,
                  label: brand.name,
                }))
              ]}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload PDF Document *
            </label>
            <input
              type="file"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              onChange={handleFileChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              Only PDF, DOC, DOCX, JPG, JPEG, PNG files are accepted
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Document Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              placeholder="Leave blank to use filename"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              placeholder="Add a short description of the document"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              disabled={!file || !formData.franchisor_id || !formData.name || loading}
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Uploading...</span>
                </>
              ) : (
                <span>Upload</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

interface BulkUploadModalProps {
  franchisors: FranchisorDropdown[];
  onClose: () => void;
  onUpload: (
    files: File[],
    franchisor_id: string | null, // allow null
    is_active: boolean
  ) => void;
  loading: boolean;
}

const BulkUploadModal: React.FC<BulkUploadModalProps> = ({
  franchisors,
  onClose,
  onUpload,
  loading,
}) => {
  const [files, setFiles] = useState<File[]>([]);
  const [franchisorId, setFranchisorId] = useState(""); // always string

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files);
      const invalid = selectedFiles.find(
        (file) => !allowedTypes.includes(file.type)
      );
      if (invalid) {
        e.target.value = "";
        setFiles([]);
        toast.error("Invalid file type.");
        return;
      }
      setFiles(selectedFiles);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (files.length === 0) return;
    onUpload(files, franchisorId || null, true);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Bulk Upload Documents
          </h2>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Franchisor *
            </label>
            <Select
              value={franchisorId}
              onChange={(val) =>setFranchisorId (val)}
              placeholder="Select a Franchisor"
              options={[
                ...franchisors.map((brand) => ({
                  value: brand.id,
                  label: brand.name,
                }))
              ]}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Files *
            </label>
            <input
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              onChange={handleFileChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
            {files.length > 0 && (
              <ul className="mt-2 text-xs text-gray-700 list-disc pl-5">
                {files.map((file) => (
                  <li key={file.name}>{file.name}</li>
                ))}
              </ul>
            )}
          </div>
          <div className="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              disabled={files.length === 0 || loading}
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Uploading...</span>
                </>
              ) : (
                <span>Upload</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

interface ViewModalProps {
  loading: boolean;
  document: DocumentItem | null;
  franchisors: FranchisorDropdown[];
  onClose: () => void;
}

const DocumentViewModal: React.FC<ViewModalProps> = ({
  loading,
  document,
  franchisors,
  onClose,
}) => {
  const franchise = document
    ? franchisors.find((f) => f.id === document.franchisor_id)
    : undefined;
  const franchiseName = franchise ? franchise.name : "Unknown";
  const status = document?.is_active ? "Active" : "Inactive";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Document Details
          </h2>
        </div>
        {loading || !document ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading...</span>
          </div>
        ) : (
          <div className="p-6">
            {/* Header */}
            <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {document.name}
                </h1>
                <p className="text-sm text-gray-500 mt-1">ID: {document.id}</p>
              </div>
              <div className="mt-4 md:mt-0">
                <span
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    document.is_active
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {status}
                </span>
              </div>
            </div>
            {/* Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <Folder className="w-5 h-5 text-gray-500" />
                  <h3 className="font-medium text-gray-900">Franchisor</h3>
                </div>
                <p className="text-gray-700 ml-8">{franchiseName}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <Info className="w-5 h-5 text-gray-500" />
                  <h3 className="font-medium text-gray-900">Description</h3>
                </div>
                <p className="text-gray-700 ml-8">
                  {document.description || (
                    <span className="text-gray-400">No description</span>
                  )}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <FileType className="w-5 h-5 text-gray-500" />
                  <h3 className="font-medium text-gray-900">File Type</h3>
                </div>
                <p className="text-gray-700 ml-8">{document.file_type || "File type not identified"}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <FileIcon className="w-5 h-5 text-gray-500" />
                  <h3 className="font-medium text-gray-900">File Size</h3>
                </div>
                <p className="text-gray-700 ml-8">{document.file_size !== undefined && document.file_size !== null ?`${(
                  Number(document.file_size) /
                  (1024 * 1024)
                )?.toFixed(2)} MB` : 'N/A'}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <FileText className="w-5 h-5 text-gray-500" />
                  <h3 className="font-medium text-gray-900">File Path</h3>
                </div>
                <p
                  className="text-gray-700 ml-8"
                  style={{
                    wordBreak: "break-all",
                  }}
                  title={document.file_path}
                >
                  {document.file_path}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <User className="w-5 h-5 text-gray-500" />
                  <h3 className="font-medium text-gray-900">User ID</h3>
                </div>
                <p className="text-gray-700 ml-8">{document.user_id}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <Calendar className="w-5 h-5 text-gray-500" />
                  <h3 className="font-medium text-gray-900">Created At</h3>
                </div>
                <p className="text-gray-700 ml-8">
                  {document.created_at ? new Date(document.created_at)?.toLocaleString() : 'N/A'}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <Edit2 className="w-5 h-5 text-gray-500" />
                  <h3 className="font-medium text-gray-900">Last Updated</h3>
                </div>
                <p className="text-gray-700 ml-8">
                  {document.updated_at ? new Date(document.updated_at)?.toLocaleString() : 'N/A'}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg md:col-span-2">
                <div className="flex items-center space-x-3 mb-2">
                  <LinkIcon className="w-5 h-5 text-gray-500" />
                  <h3 className="font-medium text-gray-900">Document</h3>
                </div>
                <a
                  href={document.file_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 ml-8"
                >
                  View Document
                </a>
              </div>
            </div>
            {/* Action Buttons */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Documents;
