import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Loader2,
  Layers,
  Calendar,
} from "lucide-react";
import { Category, categoryService } from "../../services/categoryService";
import {
  subcategoryService,
  Subcategory,
} from "../../services/subcategoryService";
import toast from "react-hot-toast";
import debounce from "lodash.debounce";
import Select from "../common/Select";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
} from "@tanstack/react-table";

const Subcategories: React.FC = () => {
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [tableLoading, setTableLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingSubcategory, setEditingSubcategory] = useState<Subcategory | null>(null);
  const [viewingSubcategory, setViewingSubcategory] = useState<Subcategory | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [togglingStatus, setTogglingStatus] = useState<string | null>(null);
  const [savingSubcategory, setSavingSubcategory] = useState(false);
  const [deletingSubcategory, setDeletingSubcategory] = useState<string | null>(
    null
  );
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
  const [totalItems, setTotalItems] = useState(0);
  const [isActiveFilter, setIsActiveFilter] = useState<boolean | null>(null);

  useEffect(() => {
    categoryService
      .getCategories(0, 100, "")
      .then(({ items }) => setCategories(items))
      .catch(() => toast.error("Failed to load categories."));
  }, []);

  const fetchSubcategories = useCallback(
    async (
      params: {
        skip?: number;
        limit?: number;
        search?: string;
        is_active: boolean | null;
      } = {
        skip: 0,
        limit: 10,
        search: "",
        is_active: null,
      }
    ) => {
      setLoading(true);
      try {
        const { items, pagination } = await subcategoryService.getSubcategories(
          undefined,
          params.search || "",
          params.skip ?? 0,
          params.limit ?? 10,
          params.is_active
        );
        const filteredItems = params.search
          ? items.filter(
              (cat: Subcategory) =>
                cat.name.toLowerCase().includes(params.search!.toLowerCase()) ||
                cat.description
                  .toLowerCase()
                  .includes(params.search!.toLowerCase())
            )
          : items;
        setSubcategories(filteredItems);
        setPagination((prev) => ({
          ...prev,
          pageIndex: pagination.currentPage - 1,
          pageSize: pagination.itemsPerPage,
        }));
        setTotalItems(pagination.totalItems);
      } catch (err) {
        toast.error("Failed to load subcategories.");
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const debouncedFetchSubcategories = useRef(
    debounce(
      async (params: {
        skip?: number;
        limit?: number;
        search?: string;
        is_active?: boolean | null;
      }) => {
        setTableLoading(true);
        try {
          const { items, pagination } =
            await subcategoryService.getSubcategories(
              undefined,
              params.search || "",
              params.skip ?? 0,
              params.limit ?? 10,
              params.is_active
            );

          const filteredItems = params.search
            ? items.filter(
                (cat: Subcategory) =>
                  cat.name
                    .toLowerCase()
                    .includes(params.search!.toLowerCase()) ||
                  cat.description
                    .toLowerCase()
                    .includes(params.search!.toLowerCase())
              )
            : items;
          setSubcategories(filteredItems);
          setPagination((prev) => ({
            ...prev,
            pageIndex: pagination.currentPage - 1,
            pageSize: pagination.itemsPerPage,
          }));
          setTotalItems(pagination.totalItems);
        } catch (err) {
          toast.error("Failed to load subcategories.");
        } finally {
          setTableLoading(false);
        }
      },
      300
    )
  ).current;

  useEffect(() => {
    fetchSubcategories({
      skip: pagination.pageIndex * pagination.pageSize,
      limit: pagination.pageSize,
      search: "",
      is_active: isActiveFilter,
    });
  }, [
    pagination.pageIndex,
    pagination.pageSize,
    isActiveFilter,
  ]);

  useEffect(() => {
    if (searchTerm) {
      debouncedFetchSubcategories({
        skip: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        search: searchTerm,
        is_active: isActiveFilter,
      });
    } else {
      fetchSubcategories({
        skip: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        search: "",
        is_active: isActiveFilter,
      });
    }
    return () => {
      debouncedFetchSubcategories.cancel();
    };
  }, [
    searchTerm,
    pagination.pageIndex,
    pagination.pageSize,
    isActiveFilter,
    debouncedFetchSubcategories,
  ]);

  const handleEdit = useCallback((subcategory: Subcategory) => {
    setEditingSubcategory(subcategory);
    setShowModal(true);
  }, []);

  const handleView = useCallback((subcategory: Subcategory) => {
    setViewingSubcategory(subcategory);
  }, []);

  const handleDelete = useCallback(async (id: string) => {
    if (confirm("Are you sure you want to delete this subcategory?")) {
      try {
        setDeletingSubcategory(id);
        const success = await subcategoryService.deleteSubcategory(id);
        if (success) {
          setSubcategories((prev) => prev.filter((s) => s.id !== id));
          toast.success("Subcategory deleted successfully!");
        } else {
          toast.error("Failed to delete subcategory. Please try again.");
        }
      } catch (err) {
        toast.error("Failed to delete subcategory. Please try again.");
      } finally {
        setDeletingSubcategory(null);
      }
    }
  }, []);

  const handleSave = useCallback(
    async (data: {
      name: string;
      description: string;
      is_active: boolean;
      category_id: string;
    }) => {
      try {
        setSavingSubcategory(true);
        if (editingSubcategory) {
          // Update
          const updated = await subcategoryService.updateSubcategory(
            editingSubcategory.id,
            data
          );
          setSubcategories((prev) =>
            prev.map((s) => (s.id === updated.id ? updated : s))
          );
          toast.success("Subcategory updated successfully!");
        } else {
          // Create
          const created = await subcategoryService.createSubcategory(
            data.category_id,
            data
          );
          setSubcategories((prev) => [...prev, created]);
          toast.success("Subcategory created successfully!");
        }
        setShowModal(false);
      } catch (err) {
        toast.error("Failed to save subcategory. Please try again.");
      } finally {
        setSavingSubcategory(false);
      }
    },
    [editingSubcategory]
  );

  const handleToggleStatus = useCallback(
    async (id: string, currentStatus: boolean) => {
      try {
        setTogglingStatus(id);
        const updated = await subcategoryService.toggleSubcategoryStatus(
          id,
          !currentStatus
        );
        setSubcategories((prev) =>
          prev.map((s) => (s.id === updated.id ? updated : s))
        );
        toast.success(
          `Subcategory ${
            !currentStatus ? "activated" : "deactivated"
          } successfully!`
        );
      } catch (err) {
        toast.error("Failed to update subcategory status. Please try again.");
      } finally {
        setTogglingStatus(null);
      }
    },
    []
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
    },
    []
  );

  const handleAddSubcategory = useCallback(() => {
    setEditingSubcategory(null);
    setShowModal(true);
  }, []);

  const handleIsActiveFilterChange = useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      const value = e.target.value;
      setIsActiveFilter(value === "all" ? null : value === "active");
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    },
    []
  );

  // TanStack Table columns
  const columns = React.useMemo<ColumnDef<Subcategory, unknown>[]>(
    () => [
      {
        accessorKey: "name",
        header: "Subcategory Name",
        cell: (info) => (
          <div>
            <div className="font-medium text-gray-900">
              {info.row.original.name}
            </div>
          </div>
        ),
      },
      {
        accessorKey: "description",
        header: "Description",
        cell: (info) => (
          <span className="text-gray-700">{info.row.original.description}</span>
        ),
      },
      {
        accessorKey: "category_id",
        header: "Category",
        cell: (info) => {
          const cat = categories.find(
            (c) => c.id === info.row.original.category_id
          );
          return <span className="text-gray-700">{cat ? cat.name : "-"}</span>;
        },
      },
      {
        accessorKey: "is_active",
        header: "Active",
        cell: (info) => {
          const subcategory = info.row.original;
          return (
            <div className="flex items-center space-x-3">
              <button
                onClick={() =>
                  handleToggleStatus(subcategory.id, subcategory.is_active)
                }
                disabled={togglingStatus === subcategory.id}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  subcategory.is_active ? "bg-blue-600" : "bg-gray-200"
                } ${
                  togglingStatus === subcategory.id
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    subcategory.is_active ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          );
        },
      },
      {
        accessorKey: "created_at",
        header: "Created Date",
        cell: (info) => (
          <span className="text-gray-700">
            {new Date(info.row.original.created_at).toLocaleDateString()}
          </span>
        ),
      },
      {
        id: "actions",
        header: "Actions",
        cell: (info) => {
          const subcategory = info.row.original;
          return (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleEdit(subcategory)}
                className="text-blue-600 hover:text-blue-800 p-1"
                title="Edit"
              >
                <Edit className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewingSubcategory(subcategory)}
                className="text-green-600 hover:text-green-800 p-1"
                title="View Details"
              >
                <Eye className="w-4 h-4" />
              </button>
              {/* <button
                onClick={() => handleDelete(subcategory.id)}
                disabled={deletingSubcategory === subcategory.id}
                className={`text-red-600 hover:text-red-800 p-1 ${
                  deletingSubcategory === subcategory.id ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                title="Delete"
              >
                {deletingSubcategory === subcategory.id ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
              </button> */}
            </div>
          );
        },
      },
    ],
    [
      categories,
      togglingStatus,
      deletingSubcategory,
      handleEdit,
      handleToggleStatus,
    ]
  );

  const table = useReactTable({
    data: subcategories,
    columns,
    pageCount: totalItems > 0 ? Math.ceil(totalItems / pagination.pageSize) : 1,
    state: {
      pagination: {
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
      },
    },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualFiltering: true,
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading subcategories...</div>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            {" "}
            Subcategories Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your business subcategories and classifications
          </p>
        </div>
        <button
          onClick={handleAddSubcategory}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Subcategory</span>
        </button>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col gap-3 md:flex-row md:items-center md:gap-4 flex-wrap">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search subcategories..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <Select
            className="w-full md:w-48"
            value={
              isActiveFilter === null
                ? "all"
                : isActiveFilter
                ? "active"
                : "inactive"
            }
            onChange={(value) => {
              if (value === "all") {
                setIsActiveFilter(null);
              } else if (value === "active") {
                setIsActiveFilter(true);
              } else {
                setIsActiveFilter(false);
              }
              setPagination((prev) => ({ ...prev, pageIndex: 0 }));
            }}
            options={[
              { value: "all", label: "All Status" },
              { value: "active", label: "Active" },
              { value: "inactive", label: "Inactive" }
            ]}
          />
        </div>
      </div>

      {/* Subcategories List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 relative">
        {tableLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading subcategories...</span>
          </div>
        ) : subcategories.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg">No subcategories found</div>
            <div className="text-gray-400 text-sm mt-1">
              Try adjusting your search or filters
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                {table.getHeaderGroups().map((headerGroup) => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <th
                        key={header.id}
                        className="text-left py-4 px-6 font-medium text-gray-900"
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody className="divide-y divide-gray-200">
                {table.getRowModel().rows.map((row) => (
                  <tr key={row.id} className="hover:bg-gray-50">
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className="py-4 px-6">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
            {/* Pagination Controls */}
            <div className="border-t border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing {pagination.pageIndex * pagination.pageSize + 1} to{" "}
                  {Math.min(
                    (pagination.pageIndex + 1) * pagination.pageSize,
                    totalItems
                  )}{" "}
                  of {totalItems} results
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <div className="flex items-center space-x-1">
                    {Array.from(
                      { length: Math.min(5, table.getPageCount()) },
                      (_, i) => {
                        const page = i + 1;
                        return (
                          <button
                            key={page}
                            onClick={() => table.setPageIndex(page - 1)}
                            className={`px-3 py-2 text-sm font-medium rounded-lg ${
                              pagination.pageIndex === page - 1
                                ? "bg-blue-600 text-white"
                                : "text-gray-500 bg-white border border-gray-300 hover:bg-gray-50"
                            }`}
                          >
                            {page}
                          </button>
                        );
                      }
                    )}
                  </div>
                  <button
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Add/Edit Modal */}
      {showModal && (
        <SubcategoryModal
          subcategory={editingSubcategory}
          onClose={() => setShowModal(false)}
          onSave={handleSave}
          saving={savingSubcategory}
          categories={categories}
        />
      )}

      {/* View Details Modal */}
      {viewingSubcategory && (
        <SubcategoryDetailsModal
          subcategory={viewingSubcategory}
          onClose={() => setViewingSubcategory(null)}
          onToggleStatus={handleToggleStatus}
          togglingStatus={togglingStatus}
          categories={categories}
        />
      )}
    </div>
  );
};

const SubcategoryModal: React.FC<{
  subcategory: Subcategory | null;
  onClose: () => void;
  onSave: (data: { name: string; description: string; is_active: boolean; category_id: string }) => void;
  saving: boolean;
  categories: Category[];
}> = ({ subcategory, onClose, onSave, saving, categories }) => {
  const [formData, setFormData] = useState({
    name: subcategory?.name || '',
    description: subcategory?.description || '',
    is_active: subcategory?.is_active ?? true,
    category_id: subcategory?.category_id || (categories[0]?.id || '')
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {subcategory ? 'Edit Subcategory' : 'Add New Subcategory'}
          </h2>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subcategory Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              disabled={saving}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              required
              disabled={saving}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category
            </label>
            <select
              value={formData.category_id}
              onChange={(e) => setFormData({ ...formData, category_id: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              disabled={saving}
            >
              {categories.map((cat) => (
                <option key={cat.id} value={cat.id}>{cat.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                disabled={saving}
              />
              <span className="ml-2 text-sm text-gray-700">Active Subcategory</span>
            </label>
          </div>
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              disabled={saving}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>{subcategory ? 'Updating...' : 'Creating...'}</span>
                </>
              ) : (
                <span>{subcategory ? 'Update' : 'Create'}</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const SubcategoryDetailsModal: React.FC<{
  subcategory: Subcategory;
  onClose: () => void;
  onToggleStatus: (id: string, currentStatus: boolean) => void;
  togglingStatus: string | null;
  categories: Category[];
}> = ({ subcategory, onClose, onToggleStatus, togglingStatus, categories }) => {
  const parentCategory = categories.find(c => c.id === subcategory.category_id);
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Subcategory Details</h2>
        </div>
        <div className="p-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{subcategory.name}</h1>
              <p className="text-sm text-gray-500 mt-1">ID: {subcategory.id}</p>
              <p className="text-sm text-gray-500 mt-1">Category: {parentCategory ? parentCategory.name : '-'}</p>
            </div>
            <div className="mt-4 md:mt-0">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => onToggleStatus(subcategory.id, subcategory.is_active)}
                  disabled={togglingStatus === subcategory.id}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    subcategory.is_active ? 'bg-blue-600' : 'bg-gray-200'
                  } ${togglingStatus === subcategory.id ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      subcategory.is_active ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <Layers className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">Description</h3>
              </div>
              <p className="text-gray-700 ml-8">{subcategory.description}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <Calendar className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">Created Date</h3>
              </div>
              <p className="text-gray-700 ml-8">{new Date(subcategory.created_at).toLocaleDateString()}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <Calendar className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">Last Updated</h3>
              </div>
              <p className="text-gray-700 ml-8">{new Date(subcategory.updated_at).toLocaleDateString()}</p>
            </div>
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Close
            </button>

          </div>
        </div>
      </div>
    </div>
  );
};

export default Subcategories; 