import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { User } from '../types';
import { authService } from '../services/authService';

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string, remember: boolean) => Promise<boolean>;
  logout: () => void;
  resetPassword: (email: string, newPassword: string, confirmPassword: string) => Promise<boolean>;
  isAuthenticated: boolean;
  refreshTokens: () => Promise<boolean>;
  showRefreshDialog: boolean;
  acceptRefresh: () => void;
  rejectRefresh: () => void;
}

interface RefreshTokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(() => {
    const savedUser = localStorage.getItem('user');
    return savedUser && savedUser != "undefined" ? JSON.parse(savedUser) : null;
  });

  const [showRefreshDialog, setShowRefreshDialog] = useState(false);

  const login = async (email: string, password: string, remember: boolean): Promise<boolean> => {
    try {
      const response = await authService.login(email, password, remember);
      const details = response;
      // Save all token data
      localStorage.setItem('access_token', details?.access_token);
      localStorage.setItem('refresh_token', details.refresh_token);
      localStorage.setItem('remember_me_token', details.remember_me_token);
      localStorage.setItem('token_type', 'Bearer');
      // localStorage.setItem('token_type', details.token_type);

      // Calculate and store expiration time
      const expiresAt = new Date(Date.now() + details.expires_in * 1000);
      localStorage.setItem('token_expires_at', expiresAt.toISOString());

      // Save user data
      localStorage.setItem('user', JSON.stringify(details?.user));
      setUser(details?.user);

      return true;
    } catch (err) {
      console.error('Login failed', err);
      throw new Error(err?.response?.data?.message?.description);
      return false;
    }
  };

  const refreshTokens = async (): Promise<boolean> => {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      if (!refreshToken) {
        return false;
      }

      const response: RefreshTokenResponse = await authService.refreshToken(refreshToken);

      // Update tokens
      localStorage.setItem('access_token', response.access_token);
      localStorage.setItem('refresh_token', response.refresh_token);
      localStorage.setItem('token_type', 'Bearer');
      // localStorage.setItem('token_type', details.token_type);

      // Calculate and store new expiration time
      const expiresAt = new Date(Date.now() + response.expires_in * 1000);
      localStorage.setItem('token_expires_at', expiresAt.toISOString());

      return true;
    } catch (err) {
      console.error('Token refresh failed', err);
      return false;
    }
  };

  const acceptRefresh = async () => {
    setShowRefreshDialog(false);
    const success = await refreshTokens();
    if (!success) {
      logout();
    }
  };

  const rejectRefresh = () => {
    setShowRefreshDialog(false);
    logout();
  };

  const logout = () => {
    authService.logout();
    setUser(null);
    setShowRefreshDialog(false);
  };

  const resetPassword = async (email: string, newPassword: string, confirmPassword: string): Promise<boolean> => {
    try {
      const success = await authService.resetPassword(email, newPassword, confirmPassword);
      if (success) {
        // Optionally, you can log the user in after successful password reset
        // await login(email, newPassword, false);
      }
      return success;
    } catch (err) {
      console.error('Password reset failed', err);
      return false;
    }
  };

  // Check token expiration on mount and periodically
  useEffect(() => {
    const checkTokenExpiration = () => {
      const expiresAt = localStorage.getItem('token_expires_at');
      const refreshToken = localStorage.getItem('refresh_token');

      if (expiresAt && refreshToken) {
        const expirationTime = new Date(expiresAt);
        const now = new Date();
        const timeUntilExpiry = expirationTime.getTime() - now.getTime();

        // If token expires in less than 5 minutes, show refresh dialog
        if (timeUntilExpiry < 5 * 60 * 1000 && timeUntilExpiry > 0) {
          setShowRefreshDialog(true);
        } else if (timeUntilExpiry <= 0) {
          // Token already expired
          setShowRefreshDialog(true);
        }
      }
    };

    checkTokenExpiration();

    // Check every minute
    const interval = setInterval(checkTokenExpiration, 60000);

    return () => clearInterval(interval);
  }, []);

  const isAuthenticated = !!user;

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      resetPassword,
      isAuthenticated,
      refreshTokens,
      showRefreshDialog,
      acceptRefresh,
      rejectRefresh
    }}>
      {children}
    </AuthContext.Provider>
  );
};