import api from "./api";

export interface SampleDocument {
  id: string;
  title: string;
  url: string;
}

export interface SampleDocsResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: SampleDocument[];
  error_code: number;
}

export const sampleDocsService = {
  /**
   * Get sample documents for CSV downloads
   * Route: GET /sample-docs/
   */
  getSampleDocs: async (): Promise<SampleDocument[]> => {
    try {
      const response = await api.get<SampleDocsResponse>('/sample-docs/');
      
      if (response.data.success) {
        return response.data.data;
      } else {
        console.error('Failed to fetch sample documents:', response.data.message);
        return [];
      }
    } catch (error) {
      console.error('Error fetching sample documents:', error);
      return [];
    }
  },

  /**
   * Download a sample document
   */
  downloadSampleDoc: async (url: string, filename: string): Promise<void> => {
    try {
      // Create a temporary link to download the file
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading sample document:', error);
      throw error;
    }
  }
};
