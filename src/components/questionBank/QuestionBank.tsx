import React, { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { Search, Loader2, <PERSON>U<PERSON>, <PERSON>Down, ArrowUpDown, Filter } from "lucide-react";
import toast from "react-hot-toast";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
  SortingState,
} from "@tanstack/react-table";
import {
  questionsService,
  QuestionItem,
  FranchisorDropdown,
} from "../../services/questionsService";
import { leadService } from "../../services/leadService";
import Select from "../common/Select";
import Pagination from "../common/Pagination";

const QuestionBank: React.FC = () => {
  const isRequestInProgress = useRef(false);
  const [questions, setQuestions] = useState<QuestionItem[]>([]);
  const [loading, setLoading] = useState({ table: false, actions: false, leads: true });
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
  const [totalItems, setTotalItems] = useState(0);
  const [franchisors, setFranchisors] = useState<FranchisorDropdown[]>([]);
  const [leads, setLeads] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [franchiseFilter, setFranchiseFilter] = useState("");
  const [sorting, setSorting] = useState<SortingState>([]);

  // Load franchisors for filter
  useEffect(() => {
    const fetchFranchisors = async () => {
      try {
        setLoading(prev => ({ ...prev, actions: true }));
        const res = await questionsService.getFranchisorsDropdown();
        if (res.success) setFranchisors(res.data.details.franchisors);
      } catch (err) {
        console.error("Error loading brands", err);
        toast.error("Failed to load brands");
      } finally {
        setLoading(prev => ({ ...prev, actions: false }));
      }
    };
    fetchFranchisors();
  }, []);

  // Load leads for name mapping (temporary patch)
  useEffect(() => {
    const fetchLeads = async () => {
      try {
        setLoading(prev => ({ ...prev, leads: true }));
        const res = await leadService.getLeads({
          skip: 0,
          limit: 100, // Large size to get most leads for mapping
        });
        if (res.success && res.data?.items) {
          setLeads(res.data.items);
        }
      } catch (err) {
        console.error("Error loading leads for name mapping", err);
        // Don't show error toast as this is just for display enhancement
      } finally {
        setLoading(prev => ({ ...prev, leads: false }));
      }
    };
    fetchLeads();
  }, []);

  // Debounce search
  useEffect(() => {
    const t = setTimeout(() => setDebouncedSearchTerm(searchTerm), 500);
    return () => clearTimeout(t);
  }, [searchTerm]);

  const loadQuestions = useCallback(async () => {
    if (isRequestInProgress.current) return;
    try {
      isRequestInProgress.current = true;
      setLoading(prev => ({ ...prev, table: true }));

      const filters = {
        page: pagination.pageIndex + 1,
        page_size: pagination.pageSize,
        search: debouncedSearchTerm || undefined,
        franchisor_id: franchiseFilter || undefined,
        sort_by: sorting[0]?.id as "name" | "created_at" | undefined,
        sort_order: sorting.length > 0
          ? (sorting[0]?.desc ? "desc" : "asc") as "asc" | "desc"
          : undefined,
      };

      const res = await questionsService.getAllQuestionBanks(filters);
      console.log("data", res);
      setQuestions(res.data.items ?? []);
      setTotalItems(res.data.total_count ?? 0);
    } catch (err) {
      console.error("Failed to load questions", err);
      toast.error("Failed to load questions");
    } finally {
      setLoading(prev => ({ ...prev, table: false }));
      isRequestInProgress.current = false;
    }
  }, [
    pagination.pageIndex,
    pagination.pageSize,
    debouncedSearchTerm,
    franchiseFilter,
    sorting,
  ]);

  useEffect(() => {
    loadQuestions();
  }, [pagination.pageIndex, debouncedSearchTerm, franchiseFilter, sorting]);

  const getFranchiseName = (id: string) => {
    if (loading.actions) return "Loading...";
    const franchise = franchisors.find((f) => f.id === id);
    return franchise?.name || "Unknown";
  };

  const getLeadName = (leadId: string) => {
    if (!leadId) return "Unknown Lead";
    const lead = leads.find((l) => l.id === leadId);
    if (lead) {
      return `${lead.first_name || ''} ${lead.last_name || ''}`.trim() || lead.name || "Unknown Lead";
    }
    return leadId; // Fallback to showing ID if lead not found
  };

  const columns = useMemo<ColumnDef<QuestionItem>[]>(() => [
    {
      accessorKey: "name",
      header: "Question Name",
    },
    {
      accessorKey: "lead_id",
      header: "Lead Name",
      cell: info => (
        <span>{getLeadName(info.getValue() as string)}</span>
      )
    },
    {
      accessorKey: "franchisor_id",
      header: "Franchisor Name",
      cell: info => (
        <span>{getFranchiseName(info.getValue() as string)}</span>
      )
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell: info => {
        const date = new Date(info.getValue() as string);
        return <span>{date.toLocaleDateString()}</span>;
      },
    },
  ], [franchisors, leads, loading.actions, loading.leads]);

  const table = useReactTable({
    data: questions,
    columns,
    pageCount: Math.ceil(totalItems / pagination.pageSize),
    state: {
      pagination,
      sorting,
    },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualSorting: true,
  });

  const isFilterActive = searchTerm.trim() !== "" || franchiseFilter !== "";

  // Show full page loading only on initial load when both are loading
  if (loading.table && loading.leads) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading Question Bank...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Question Bank</h1>
          <p className="text-gray-600 mt-1">Manage reusable questions</p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col md:flex-row gap-4 items-center flex-wrap">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search questions..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="w-full md:w-[200px] flex-none">
            <Select
              value={franchiseFilter || "all"}
              onChange={(value) => {
                setFranchiseFilter(value === "all" ? "" : value);
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              options={[
                { value: "all", label: "All Franchisor" },
                ...franchisors.map((brand) => ({
                  value: brand.id,
                  label: brand.name,
                }))
              ]}
            />
        </div>
          {/* Clear Filters Button */}
          <div className="w-full md:w-auto flex-none">
            <button
              onClick={()=>{
                setSearchTerm("");
                setFranchiseFilter("");
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 w-full md:w-auto justify-center relative"
            >
              <Filter className="w-4 h-4" />
              <span>Clear Filters</span>

              {isFilterActive && (
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 mt-6">
        {loading.table || loading.leads ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">
              {loading.table && loading.leads ? "Loading questions and leads..." :
               loading.table ? "Loading questions..." : "Loading lead names..."}
            </span>
          </div>
        ) : Array.isArray(questions) && questions?.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg">No questions found</div>
            <div className="text-gray-400 text-sm mt-1">
              Try adjusting your search or filters
            </div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => {
                        const isSortable = header.column.id === "name" || header.column.id === "created_at";

                        return (
                          <th
                            key={header.id}
                            onClick={isSortable ? header.column.getToggleSortingHandler() : undefined}
                            className={`text-left py-4 px-6 font-medium text-gray-900 ${
                              isSortable ? "cursor-pointer select-none" : ""
                            }`}
                          >
                            <div className="flex items-center gap-1">
                              {flexRender(header.column.columnDef.header, header.getContext())}
                              {isSortable && (
                                <>
                                  {header.column.getIsSorted() === "asc" ? (
                                    <ArrowUp className="w-4 h-4 text-blue-600" />
                                  ) : header.column.getIsSorted() === "desc" ? (
                                    <ArrowDown className="w-4 h-4 text-blue-600" />
                                  ) : (
                                    <ArrowUpDown className="w-4 h-4 text-gray-400" />
                                  )}
                                </>
                              )}
                            </div>
                          </th>
                        );
                      })}
                    </tr>
                  ))}
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {table.getRowModel().rows.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50">
                      {row.getVisibleCells().map((cell) => (
                        <td key={cell.id} className="py-4 px-6">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <Pagination
              pageIndex={pagination.pageIndex}
              pageSize={pagination.pageSize}
              totalCount={totalItems}
              pageCount={table.getPageCount()}
              onPageChange={(page) => setPagination((prev) => ({ ...prev, pageIndex: page }))}
              onPrevious={() => table.previousPage()}
              onNext={() => table.nextPage()}
              canPreviousPage={table.getCanPreviousPage()}
              canNextPage={table.getCanNextPage()}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default QuestionBank;
