export const allowedTypes = [
  "application/pdf",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
  "application/msword", // .doc
  "image/jpeg", // .jpg, .jpeg
  "image/png", // .png
];

export const fileTypeLabels: Record<string, string> = {
  "application/pdf": "PDF",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    "DOCX",
  "application/msword": "DOC",
  "image/jpeg": "JPEG",
  "image/png": "PNG",
};

export function getFileTypeLabel(type: string): string {
  return fileTypeLabels[type] || type;
}
