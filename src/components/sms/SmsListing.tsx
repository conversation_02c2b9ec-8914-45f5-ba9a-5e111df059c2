import React, { useState, useEffect, useCallback } from 'react';
import {
  MessageSquare,
  Search,
  Filter,
  ChevronDown,
  ChevronRight,
  Trash2,
  Loader2,
  User,
  Calendar,
  Building2,
  RefreshCw
} from 'lucide-react';
import { conversationService, ConversationMessage } from '../../services/conversationService';
import { questionsService, FranchisorDropdown } from '../../services/questionsService';
import toast from 'react-hot-toast';
import Select from '../common/Select';
import Pagination from '../common/Pagination';
import DeleteConfirmModal from '../common/DeleteConfirmModal';

interface GroupedConversations {
  [leadId: string]: {
    leadInfo: {
      id: string;
      first_name: string;
      last_name: string;
      phone?: string;
      email?: string;
    };
    messages: ConversationMessage[];
    franchisorInfo: {
      id: string;
      name: string;
    };
  };
}

const SmsListing: React.FC = () => {
  const [conversations, setConversations] = useState<ConversationMessage[]>([]);
  const [groupedConversations, setGroupedConversations] = useState<GroupedConversations>({});
  const [franchisors, setFranchisors] = useState<FranchisorDropdown[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [franchiseFilter, setFranchiseFilter] = useState('');
  const [expandedLeads, setExpandedLeads] = useState<Set<string>>(new Set());
  const [deletingMessageId, setDeletingMessageId] = useState<string | null>(null);
  const [deleteModal, setDeleteModal] = useState<{ open: boolean; messageId: string | null }>({
    open: false,
    messageId: null
  });

  // Pagination
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 20,
  });
  const [totalItems, setTotalItems] = useState(0);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      if (searchTerm !== debouncedSearchTerm) {
        setPagination(prev => ({ ...prev, pageIndex: 0 }));
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, debouncedSearchTerm]);

  // Load franchisors for filter
  useEffect(() => {
    const loadFranchisors = async () => {
      try {
        const response = await questionsService.getFranchisorsDropdown();
        if (response.success) {
          setFranchisors(response.data.details.franchisors);
        }
      } catch (error) {
        console.error('Error loading franchisors:', error);
        toast.error('Failed to load franchisors');
      }
    };

    loadFranchisors();
  }, []);

  // Load conversations
  const loadConversations = useCallback(async () => {
    try {
      setLoading(true);
      const response = await conversationService.getAllConversations({
        page: pagination.pageIndex + 1,
        per_page: pagination.pageSize,
        search: debouncedSearchTerm || undefined,
        franchisor_id: franchiseFilter || undefined,
        order: 'desc'
      });

      if (response.success) {
        const items = response.data?.items || [];
        setConversations(items);
        setTotalItems(response.pagination?.total_items || 0);

        // Group conversations by lead
        const grouped = groupConversationsByLead(items);
        setGroupedConversations(grouped);
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
      toast.error('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  }, [pagination.pageIndex, pagination.pageSize, debouncedSearchTerm, franchiseFilter]);

  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  // Group conversations by lead
  const groupConversationsByLead = (messages: ConversationMessage[]): GroupedConversations => {
    const grouped: GroupedConversations = {};

    if (!messages || !Array.isArray(messages)) {
      return grouped;
    }

    messages.forEach(message => {
      if (!grouped[message.lead_id]) {
        grouped[message.lead_id] = {
          leadInfo: {
            id: message.lead_id,
            first_name: message.lead_first_name,
            last_name: message.lead_last_name || '',
          },
          messages: [],
          franchisorInfo: {
            id: message.franchisor_id,
            name: message.franchisor_name
          }
        };
      }
      grouped[message.lead_id].messages.push(message);
    });

    // Sort messages within each lead by date (newest first)
    Object.keys(grouped).forEach(leadId => {
      grouped[leadId].messages.sort((a, b) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    });

    return grouped;
  };

  // Toggle lead expansion
  const toggleLeadExpansion = (leadId: string) => {
    const newExpanded = new Set(expandedLeads);
    if (newExpanded.has(leadId)) {
      newExpanded.delete(leadId);
    } else {
      newExpanded.add(leadId);
    }
    setExpandedLeads(newExpanded);
  };

  // Handle delete message
  const handleDeleteMessage = async (messageId: string) => {
    setDeleteModal({ open: true, messageId });
  };

  const confirmDeleteMessage = async () => {
    if (!deleteModal.messageId) return;

    try {
      setDeletingMessageId(deleteModal.messageId);
      await conversationService.deleteMessage(deleteModal.messageId);
      toast.success('Message deleted successfully');
      
      // Reload conversations
      await loadConversations();
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error('Failed to delete message');
    } finally {
      setDeletingMessageId(null);
      setDeleteModal({ open: false, messageId: null });
    }
  };

  // Clear filters
  const clearFilters = () => {
    setSearchTerm('');
    setFranchiseFilter('');
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
    setDebouncedSearchTerm('');
  };

  const isFilterActive = searchTerm.trim() !== '' || franchiseFilter !== '';

  if (loading && (!conversations || conversations.length === 0)) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading SMS conversations...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">SMS Conversations</h1>
          <p className="text-gray-600 mt-1">
            View and manage SMS conversations with leads
          </p>
        </div>
        <button
          onClick={loadConversations}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Refresh</span>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="w-full md:w-auto flex-1 min-w-[200px]">
            <Select
              value={franchiseFilter}
              onChange={(value) => {
                setFranchiseFilter(value);
                setPagination(prev => ({ ...prev, pageIndex: 0 }));
              }}
              placeholder="Select Franchisor"
              options={[
                { value: "", label: "All Franchisors" },
                ...franchisors.map((franchisor) => ({
                  value: franchisor.id,
                  label: franchisor.name,
                }))
              ]}
            />
          </div>

          <div className="w-full md:w-auto flex-none">
            <button
              onClick={clearFilters}
              disabled={!isFilterActive}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 w-full md:w-auto justify-center relative"
            >
              <Filter className="w-4 h-4" />
              <span>Clear Filters</span>
              {isFilterActive && (
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Conversations List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading conversations...</span>
          </div>
        ) : Object.keys(groupedConversations).length === 0 ? (
          <div className="text-center py-12">
            <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <div className="text-gray-500 text-lg">No conversations found</div>
            <div className="text-gray-400 text-sm mt-1">
              Try adjusting your search or filters
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {Object.entries(groupedConversations).map(([leadId, data]) => (
              <div key={leadId} className="p-4">
                {/* Lead Header */}
                <div
                  className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-3 rounded-lg transition-colors"
                  onClick={() => toggleLeadExpansion(leadId)}
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      {expandedLeads.has(leadId) ? (
                        <ChevronDown className="w-5 h-5 text-gray-400" />
                      ) : (
                        <ChevronRight className="w-5 h-5 text-gray-400" />
                      )}
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-blue-600" />
                      </div>
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h3 className="font-semibold text-gray-900">
                          {data.leadInfo.first_name} {data.leadInfo.last_name}
                        </h3>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          {data.messages.length} message{data.messages.length !== 1 ? 's' : ''}
                        </span>
                      </div>

                      <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Building2 className="w-4 h-4" />
                          <span>{data.franchisorInfo.name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>
                            Last: {new Date(data.messages[0]?.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="text-sm text-gray-400">
                    Click to {expandedLeads.has(leadId) ? 'collapse' : 'expand'}
                  </div>
                </div>

                {/* Messages */}
                {expandedLeads.has(leadId) && (
                  <div className="mt-4 ml-8 space-y-2 max-h-96 overflow-y-auto">
                    {data.messages.map((message) => {
                      const isFromLead = message.sender === 'lead';
                      return (
                        <div
                          key={message.id}
                          className={`flex ${isFromLead ? 'justify-start' : 'justify-end'}`}
                        >
                          <div className={`max-w-lg lg:max-w-2xl xl:max-w-4xl ${isFromLead ? 'order-1' : 'order-2'}`}>
                            {/* Message bubble */}
                            <div className={`rounded-lg p-3 ${
                              isFromLead
                                ? 'bg-gray-100 text-gray-900'
                                : 'bg-blue-600 text-white'
                            }`}>
                              <div className="text-sm whitespace-pre-wrap">
                                {message.message}
                              </div>
                            </div>

                            {/* Message info */}
                            <div className={`mt-1 flex items-center space-x-2 text-xs text-gray-500 ${
                              isFromLead ? 'justify-start' : 'justify-end'
                            }`}>
                              <span>{new Date(message.created_at).toLocaleString()}</span>
                              <span className={`px-2 py-1 rounded-full ${
                                isFromLead
                                  ? 'bg-gray-200 text-gray-700'
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {isFromLead ? 'Lead' : 'System'}
                              </span>

                              {/* Delete button */}
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteMessage(message.id);
                                }}
                                disabled={deletingMessageId === message.id}
                                className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                                title="Delete message"
                              >
                                {deletingMessageId === message.id ? (
                                  <Loader2 className="w-3 h-3 animate-spin" />
                                ) : (
                                  <Trash2 className="w-3 h-3" />
                                )}
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {!loading && Object.keys(groupedConversations).length > 0 && (
          <div className="border-t border-gray-200 px-4 py-3">
            <Pagination
              pageIndex={pagination.pageIndex}
              pageSize={pagination.pageSize}
              totalCount={totalItems}
              pageCount={Math.ceil(totalItems / pagination.pageSize)}
              onPageChange={(page) => setPagination(prev => ({ ...prev, pageIndex: page }))}
              onPrevious={() => setPagination(prev => ({ ...prev, pageIndex: Math.max(0, prev.pageIndex - 1) }))}
              onNext={() => setPagination(prev => ({ ...prev, pageIndex: prev.pageIndex + 1 }))}
              canPreviousPage={pagination.pageIndex > 0}
              canNextPage={pagination.pageIndex < Math.ceil(totalItems / pagination.pageSize) - 1}
            />
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        open={deleteModal.open}
        title="Delete Message"
        message="Are you sure you want to delete this SMS message? This action cannot be undone."
        onCancel={() => setDeleteModal({ open: false, messageId: null })}
        onConfirm={confirmDeleteMessage}
        loading={!!deletingMessageId}
      />
    </div>
  );
};

export default SmsListing;
