import React from "react";
import { useNavigate } from "react-router-dom";

const ForgotPasswordLayout = ({
  title,
  description,
  children,
}: {
  title: string;
  description: string;
  children: React.ReactNode;
}) => {
  const navigate = useNavigate();
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
            <p className="text-gray-600 mt-2">{description}</p>
          </div>
          {children}
          <button
              onClick={() => navigate("/login")}
              className="w-full mt-4 text-gray-600 hover:text-gray-800 font-medium"
            >
              Back to Login
            </button>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordLayout;
