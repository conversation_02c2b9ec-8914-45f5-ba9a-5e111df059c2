import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from "react";
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
  ColumnDef,
  SortingState,
} from "@tanstack/react-table";
import {
  Search,
  Loader2,
  <PERSON><PERSON><PERSON>,
  <PERSON>Up,
  ArrowDown,
  ArrowUpDown,
  Trash2,
  Plus,
  Filter,
} from "lucide-react";
import toast from "react-hot-toast";
import {
  questionsService,
  FranchisorDropdown,
} from "../../services/questionsService";
import {
  EscalationQuestion,
  escalationQuestionsService,
} from "../../services/escalationQuestionsService";
import { leadService } from "../../services/leadService";
import Select from "../common/Select";
import Pagination from "../common/Pagination";

const EscalationQuestions: React.FC = () => {
  const isRequestInProgress = useRef(false);

  const [questions, setQuestions] = useState<EscalationQuestion[]>([]);
  const [franchisors, setFranchisors] = useState<FranchisorDropdown[]>([]);
  const [leads, setLeads] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [franchiseFilter, setFranchiseFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState({ table: false, actions: false, leads: true });
  const [updatingRowId, setUpdatingRowId] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [editingQuestion, setEditingQuestion] = useState<EscalationQuestion | null>(null);
  const [showAnswerModal, setShowAnswerModal] = useState(false);
  const [modalAnswers, setModalAnswers] = useState<string[]>([]);

  // Debounce search
  useEffect(() => {
    const t = setTimeout(() => setDebouncedSearchTerm(searchTerm), 400);
    return () => clearTimeout(t);
  }, [searchTerm]);

  useEffect(() => {
    const loadFranchisors = async () => {
      try {
        const res = await questionsService.getFranchisorsDropdown();
        if (res.success) setFranchisors(res.data.details.franchisors);
      } catch {
        toast.error("Failed to load brands");
      }
    };
    loadFranchisors();
  }, []);

  // Load leads for name mapping (temporary patch)
  useEffect(() => {
    const fetchLeads = async () => {
      try {
        setLoading(prev => ({ ...prev, leads: true }));
        const res = await leadService.getLeads({
          skip: 0,
          limit: 100, // Large size to get most leads for mapping
        });
        if (res.success && res.data?.items) {
          setLeads(res.data.items);
        }
      } catch (err) {
        console.error("Error loading leads for name mapping", err);
        // Don't show error toast as this is just for display enhancement
      } finally {
        setLoading(prev => ({ ...prev, leads: false }));
      }
    };
    fetchLeads();
  }, []);

  const loadQuestions = useCallback(async () => {
    if (isRequestInProgress.current) return;
    try {
      isRequestInProgress.current = true;
      setLoading((prev) => ({ ...prev, table: true }));

      const filters = {
        lead_id: null,
        franchisor_id: franchiseFilter || undefined,
        support_status: statusFilter || undefined,
        search: debouncedSearchTerm || undefined,
        sort_by: sorting[0]?.id as "name" | "support_status" | "created_at" | undefined,
        sort_order: sorting.length > 0 ? (sorting[0]?.desc ? "desc" : "asc") as "asc" | "desc": undefined,
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
      };

      const res = await escalationQuestionsService.getAllEscalationQuestions(filters);
      setQuestions(res.items);
      setTotalItems(res.total_count || 0);
    } catch {
      toast.error("Failed to load questions");
    } finally {
      setLoading((prev) => ({ ...prev, table: false }));
      isRequestInProgress.current = false;
    }
  }, [pagination.pageIndex,pagination.pageSize, debouncedSearchTerm, franchiseFilter, statusFilter, sorting]);

  useEffect(() => {
    loadQuestions();
  }, [pagination.pageIndex,
  pagination.pageSize,
  debouncedSearchTerm,
  franchiseFilter,
  statusFilter,
  sorting,]);

  const updateStatus = async (id: string, status: string) => {
    setUpdatingRowId(id);
    try {
      const res = await escalationQuestionsService.updateEscalationStatus(id, status);
      if(res.success && res.data){
        toast.success(res.message?.title || "Support status updated");
        setQuestions(prev =>
          prev.map(q =>
            q.id === id ? { ...q, support_status: res.data.support_status || status } : q
          )
        );
      }
    } catch(error) {
      toast.error(error?.response?.data?.message?.description || "Failed to update status");
    } finally {
      setUpdatingRowId(null);
    }
  };

  const handleSaveAnswers = async () => {
    if (!editingQuestion) return;
    try {
      await escalationQuestionsService.updateEscalationAnswer(editingQuestion.id, modalAnswers);
      toast.success("Answers updated");
      setShowAnswerModal(false);
      loadQuestions();
    } catch {
      toast.error("Failed to update answers");
    }
  };

  const getLeadName = (leadId: string) => {
    if (!leadId) return "Unknown Lead";
    const lead = leads.find((l) => l.id === leadId);
    if (lead) {
      return `${lead.first_name || ''} ${lead.last_name || ''}`.trim() || lead.name || "Unknown Lead";
    }
    return leadId; // Fallback to showing ID if lead not found
  };

  const columns = useMemo<ColumnDef<EscalationQuestion>[]>(
    () => [
      {
        accessorKey: "name",
        header: "Question",
      },
      {
        accessorKey: "lead_id",
        header: "Lead Name",
        cell: info => (
          <span>{getLeadName(info.getValue() as string)}</span>
        )
      },
      {
        accessorKey: "franchisor_id",
        header: "Franchisor",
        cell: (info) => {
          const brand = franchisors.find((f) => f.id === info.getValue());
          return brand?.name || "Unknown";
        },
      },
      {
        accessorKey: "support_status",
        header: "Status",
        cell: (info) => {
          const row = info.row.original;
          const isUpdating = updatingRowId === row.id;

          return (
            <div className="relative w-[160px]">
              <Select
                value={row.support_status}
                onChange={(value) => updateStatus(row.id, value)}
                disabled={isUpdating}
                options={[
                  { value: "Pending", label: "Pending" },
                  { value: "Resolved", label: "Resolved" },
                  { value: "Not Applicable", label: "Not Applicable" },
                ]}
              />
              {isUpdating && (
                <div className="absolute inset-0 bg-white/50 flex items-center justify-center rounded">
                  <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                </div>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "created_at",
        header: "Created At",
        cell: (info) => {
          const date = new Date(info.getValue() as string);
          return <span>{date.toLocaleDateString()}</span>;
        },
      },
      {
        id: "actions",
        header: "Actions",
        cell: ({ row }) => (
          <button
            onClick={() => {
              setEditingQuestion(row.original);
              setModalAnswers(row.original.answer || []);
              setShowAnswerModal(true);
            }}
            className="text-blue-600 hover:text-blue-800 flex items-center gap-1"
            title="Edit Answers"
          >
            <BookOpen className="w-4 h-4" />
            <span className="text-sm">Learn Answer</span>
          </button>
        ),
      },
    ],
    [franchisors, leads, loading.leads, updateStatus]
  );

  const table = useReactTable({
    data: questions,
    columns,
    pageCount: Math.ceil(totalItems / pagination.pageSize),
    state: { pagination, sorting },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    manualPagination: true,
    manualSorting: true,
    getCoreRowModel: getCoreRowModel(),
  });

  const isFilterActive = searchTerm.trim() !== "" || franchiseFilter !== "" || statusFilter !== "";

  // Show full page loading only on initial load when both are loading
  if (loading.table && loading.leads) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading Escalation Questions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Escalation Question Bank</h1>
        <p className="text-gray-600 mt-1">Manage escalated question bank</p>
      </div>

      {/* Filters */}
      <div className="bg-white border rounded-xl p-4 shadow-sm">
        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex-1 relative min-w-[200px]">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search questions..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg"
            />
          </div>
          <div className="min-w-[200px] w-[200px]">
            <Select
              value={franchiseFilter || "all"}
              onChange={(val) => {
                setFranchiseFilter(val === "all" ? "" : val);
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              options={[
                { value: "all", label: "All Franchisors" },
                ...franchisors.map((f) => ({ value: f.id, label: f.name })),
              ]}
            />
          </div>
          <div className="min-w-[200px] w-[200px]">
            <Select
              value={statusFilter || "all"}
              onChange={(val) => {
                setStatusFilter(val === "all" ? "" : val);
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              options={[
                { value: "all", label: "All Statuses" },
                { value: "Pending", label: "Pending" },
                { value: "Resolved", label: "Resolved" },
                { value: "Not Applicable", label: "Not Applicable" },
              ]}
            />
          </div>
          {/* Clear Filters Button */}
          <div className="w-full md:w-auto flex-none">
            <button
              onClick={() => {
                setSearchTerm("");
                setFranchiseFilter("");
                setStatusFilter("");
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 w-full md:w-auto justify-center relative"
            >
              <Filter className="w-4 h-4" />
              <span>Clear Filters</span>

              {isFilterActive && (
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-xl shadow-sm border mt-6">
        {loading.table || loading.leads ? (
          <div className="py-12 flex justify-center items-center">
            <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">
              {loading.table && loading.leads ? "Loading questions and leads..." :
               loading.table ? "Loading questions..." : "Loading lead names..."}
            </span>
          </div>
        ) : questions.length === 0 ? (
          <div className="text-center py-12 text-gray-500">No questions found</div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  {table.getHeaderGroups().map((group) => (
                    <tr key={group.id}>
                      {group.headers.map((header) => {
                        const isSortable = header.column.id === "name" || header.column.id === "created_at";
                        return (
                          <th
                            key={header.id}
                            onClick={isSortable ? header.column.getToggleSortingHandler() : undefined}
                            className={`text-left py-4 px-6 font-medium text-gray-900 ${
                              isSortable ? "cursor-pointer select-none" : ""
                            }`}
                          >
                            <div className="flex items-center gap-1">
                              {flexRender(header.column.columnDef.header, header.getContext())}
                              {isSortable && (
                                <>
                                  {header.column.getIsSorted() === "asc" ? (
                                    <ArrowUp className="w-4 h-4 text-blue-600" />
                                  ) : header.column.getIsSorted() === "desc" ? (
                                    <ArrowDown className="w-4 h-4 text-blue-600" />
                                  ) : (
                                    <ArrowUpDown className="w-4 h-4 text-gray-400" />
                                  )}
                                </>
                              )}
                            </div>
                          </th>
                        );
                      })}
                    </tr>
                  ))}
                </thead>
                <tbody className="divide-y">
                  {table.getRowModel().rows.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50">
                      {row.getVisibleCells().map((cell) => (
                        <td key={cell.id} className="px-6 py-4">
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {/* Pagination */}
            <Pagination
              pageIndex={pagination.pageIndex}
              pageSize={pagination.pageSize}
              totalCount={totalItems}
              pageCount={table.getPageCount()}
              onPageChange={(page) => setPagination((prev) => ({ ...prev, pageIndex: page }))}
              onPrevious={() => table.previousPage()}
              onNext={() => table.nextPage()}
              canPreviousPage={table.getCanPreviousPage()}
              canNextPage={table.getCanNextPage()}
            />
          </>
        )}
      </div>

      {/* Modal */}
      {showAnswerModal && editingQuestion && (
        <div className="fixed inset-0 bg-black bg-opacity-40 z-50 flex items-center justify-center mt-0!">
          <div className="bg-white p-6 rounded-xl shadow-lg w-full max-w-lg space-y-4">
            <h2 className="text-xl font-bold">Edit Answers</h2>
            <div className="space-y-3">
              {modalAnswers.map((ans, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="text"
                    className="flex-1 px-3 py-2 border rounded"
                    value={ans}
                    onChange={(e) => {
                      const updated = [...modalAnswers];
                      updated[index] = e.target.value;
                      setModalAnswers(updated);
                    }}
                  />
                  <button
                    onClick={() => {
                      setModalAnswers(modalAnswers.filter((_, i) => i !== index));
                    }}
                    className="text-red-600 hover:text-red-800"
                    title="Remove"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              ))}
              <button
                onClick={() => setModalAnswers([...modalAnswers, ""])}
                className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-2"
              >
                <Plus className="w-4 h-4" /> Add Answer
              </button>
            </div>
            <div className="flex justify-end gap-3 pt-4">
              <button
                className="px-4 py-2 border rounded text-gray-600"
                onClick={() => setShowAnswerModal(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                onClick={handleSaveAnswers}
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EscalationQuestions;
