import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ForgotPasswordLayout from "./ForgotPasswordLayout";
import * as Yup from "yup";
import { OTPInput, SlotProps } from "input-otp";
import { Formik, Form, ErrorMessage } from "formik";
import { APIErrorResponse, authService } from "../../services/authService";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

const schema = {
  otp: Yup.object().shape({
    otp: Yup.string()
      .required("OTP is required")
      .matches(/^[0-9]{6}$/, "OTP must be 6 digits"),
  })
};

const VerifyOtp = () => {
  const navigate = useNavigate();
  const user_id = localStorage.getItem("forgot_password_user_id");

  useEffect(() => {
    if (!user_id) {
      navigate("/forgot-password");
    }
  }, [user_id, navigate]);

  if (!user_id) return null;
  
  const Slot = (props: SlotProps) => (
    <div
      className={`relative w-12 h-14 border border-gray-300 rounded-lg flex items-center justify-center text-xl font-medium ${
        props.isActive
          ? "border-2 border-yellow-500 ring-1 ring-yellow-200"
          : ""
      }`}
    >
      {props.char !== null && <div>{props.char}</div>}
      {props.hasFakeCaret && (
        <div className="absolute inset-0 flex items-center justify-center animate-caret-blink">
          <div className="w-px h-5 bg-black" />
        </div>
      )}
    </div>
  );

  const handleVerifyOtp = async ({ otp }: { otp: string }) => {
    try {
      const response = await authService.verifyForgotPasswordOtp({
        otp,
        user_id: user_id!,
      });

      if (response?.success) {
        const resetCode = response.data?.details?.reset_code;
        if (resetCode) {
          localStorage.setItem("reset_code", resetCode);
          toast.success(
            response.message?.description || "OTP Verified Successfully"
          );
          navigate("/reset-password");
        } else {
          toast.error("Reset code missing in response");
        }
      } else {
        toast.error("Verification failed");
      }
    } catch (err: unknown) {
      const error = err as AxiosError<APIErrorResponse>;
      const errorMsg =
        error?.response?.data?.error?.message?.description ||
        error?.response?.data?.message?.description ||
        "Something went wrong";
      toast.error(errorMsg);
    }
  };


  return (
    <ForgotPasswordLayout
      title="Verify OTP"
      description={`Enter the 4-digit code sent to your email`}
    >
      <Formik
        initialValues={{ otp: "" }}
        validationSchema={schema.otp}
        onSubmit={handleVerifyOtp}
      >
        {({ values, setFieldValue }) => (
          <Form className="space-y-6">
            <div>
              <label htmlFor="otp" className="block text-sm font-medium mb-2">
                OTP Code
              </label>
              <OTPInput
                maxLength={6}
                name="otp"
                value={values.otp ?? ""}
                onChange={(value) => setFieldValue("otp", value)}
                containerClassName="flex"
                render={({ slots }) => (
                  <div className="w-full flex gap-2 justify-between">
                    {slots.map((slot, idx) => (
                      <Slot key={idx} {...slot} />
                    ))}
                  </div>
                )}
                inputMode="numeric"
                type="text"
              />
              <ErrorMessage
                name="otp"
                component="div"
                className="text-red-500 text-sm mt-1"
              />
            </div>
            <button
              type="submit"
              className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 px-4 rounded-lg font-medium hover:from-yellow-700 hover:to-yellow-700 focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Verify & Continue
            </button>
          </Form>
        )}
      </Formik>
    </ForgotPasswordLayout>
  );
};

export default VerifyOtp;
