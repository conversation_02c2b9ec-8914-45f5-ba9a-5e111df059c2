import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface SortableItemProps {
  id: string;
  children: React.ReactNode | ((props: { dragAttributes: any; dragListeners: any }) => JSX.Element);
}

export const SortableItem: React.FC<SortableItemProps> = ({ id, children }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id,
    // Only allow dragging from specific elements
    data: {
      type: 'question',
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 100 : 'auto',
  };

  return (
    <div ref={setNodeRef} style={style}>
      {typeof children === 'function'
        ? children({ dragAttributes: attributes, dragListeners: listeners })
        : React.Children.map(children, (child) => {
            if (React.isValidElement(child)) {
              return React.cloneElement(child, {
                dragAttributes: attributes,
                dragListeners: listeners,
              });
            }
            return child;
          })
      }
    </div>
  );
};