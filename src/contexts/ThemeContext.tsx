import React, { createContext, useContext, ReactNode } from 'react';

type ThemeContextType = {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
};

const defaultTheme: ThemeContextType = {
  primaryColor: 'yellow-500',
  secondaryColor: 'yellow-100',
  accentColor: 'yellow-600',
};

const ThemeContext = createContext<ThemeContextType>(defaultTheme);

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <ThemeContext.Provider value={defaultTheme}>
      {children}
    </ThemeContext.Provider>
  );
};