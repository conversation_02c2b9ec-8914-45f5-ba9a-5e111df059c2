import api from "./api";

export interface ConversationMessage {
  id: string;
  created_at: string;
  franchisor_id: string;
  franchisor_name: string;
  is_deleted: boolean;
  lead_first_name: string;
  lead_id: string;
  lead_last_name: string;
  message: string;
  updated_at: string;
}

export interface ConversationData {
  items: ConversationMessage[];
  current_page: number;
  total_items: number;
  total_pages: number;
}

export interface ConversationResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: ConversationData;
  pagination: {
    current_page: number;
    total_pages: number;
    items_per_page: number;
    total_items: number;
  };
  metadata: {
    timestamp: string;
    request_id: string | null;
    api_version: string;
    processing_time_ms: number | null;
  };
  error_code: string | null;
}

export interface ConversationFilters {
  lead_id: string;
  page?: number;
  per_page?: number;
  order?: 'asc' | 'desc';
}

export interface DeleteMessageResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: any;
  metadata: {
    timestamp: string;
    request_id: string | null;
    api_version: string;
    processing_time_ms: number | null;
  };
  error_code: string | null;
}

export const conversationService = {
  /**
   * GET /api/conversation/conversation-messages/lead/{lead_id}
   * Get Messages by Lead ID
   */
  getMessagesByLeadId: async (filters: ConversationFilters): Promise<ConversationResponse> => {
    const params = new URLSearchParams();
    
    if (filters.page) params.append("page", filters.page.toString());
    if (filters.per_page) params.append("per_page", filters.per_page.toString());
    if (filters.order) params.append("order", filters.order);

    const url = `/conversation/conversation-messages/lead/${filters.lead_id}?${params.toString()}`;
    
    const response = await api.get<ConversationResponse>(url);
    return response.data;
  },

  /**
   * DELETE /api/conversation/conversation-messages/{message_id}
   * Delete Conversation Message
   */
  deleteMessage: async (messageId: string): Promise<DeleteMessageResponse> => {
    const response = await api.delete<DeleteMessageResponse>(
      `/conversation/conversation-messages/${messageId}`
    );
    return response.data;
  },

  /**
   * Get all conversation messages for all leads (for SMS listing page)
   * Since we only have individual lead endpoints, we'll need to fetch leads first
   * and then get conversations for each lead
   */
  getAllConversations: async (params: {
    page?: number;
    per_page?: number;
    search?: string;
    franchisor_id?: string;
    order?: 'asc' | 'desc';
  } = {}): Promise<ConversationResponse> => {
    try {
      // First, try the direct endpoint if it exists
      const query = new URLSearchParams();

      if (params.page) query.append("page", params.page.toString());
      if (params.per_page) query.append("per_page", params.per_page.toString());
      if (params.search) query.append("search", params.search);
      if (params.franchisor_id) query.append("franchisor_id", params.franchisor_id);
      if (params.order) query.append("order", params.order);

      const url = `/conversation/conversation-messages?${query.toString()}`;

      const response = await api.get<ConversationResponse>(url);
      return response.data;
    } catch (error) {
      console.warn('Direct conversations endpoint not available, trying alternative approach');

      // Alternative: Get leads first, then their conversations
      try {
        // Import leadService to get leads
        const leadServiceModule = await import('./leadService');
        const leadService = leadServiceModule.leadService;

        const leadsResponse = await leadService.getList({
          page: 1,
          size: 100, // Get more leads to find conversations
          search: params.search || null,
          franchisor_id: params.franchisor_id || null,
          lead_source_id: null,
          lead_status_id: null,
          is_active: null,
        });

        if (!leadsResponse.success) {
          throw new Error('Failed to fetch leads');
        }

        const leads = leadsResponse.data.details.items;
        const allMessages: ConversationMessage[] = [];

        // Get conversations for each lead (limit to first few to avoid too many requests)
        const leadsToCheck = leads.slice(0, 10); // Limit to first 10 leads for performance

        for (const lead of leadsToCheck) {
          try {
            const conversationResponse = await conversationService.getMessagesByLeadId({
              lead_id: lead.id,
              page: 1,
              per_page: 50,
              order: params.order || 'desc'
            });

            if (conversationResponse.success && conversationResponse.data?.items) {
              allMessages.push(...conversationResponse.data.items);
            }
          } catch (leadError) {
            // Skip this lead if conversation fetch fails
            console.warn(`Failed to fetch conversations for lead ${lead.id}:`, leadError);
          }
        }

        // Sort all messages by created_at
        allMessages.sort((a, b) => {
          const dateA = new Date(a.created_at).getTime();
          const dateB = new Date(b.created_at).getTime();
          return params.order === 'asc' ? dateA - dateB : dateB - dateA;
        });

        // Apply pagination to the aggregated results
        const page = params.page || 1;
        const perPage = params.per_page || 20;
        const startIndex = (page - 1) * perPage;
        const endIndex = startIndex + perPage;
        const paginatedMessages = allMessages.slice(startIndex, endIndex);

        return {
          success: true,
          status: 'success',
          message: {
            title: 'Success',
            description: 'Conversations retrieved successfully'
          },
          data: {
            items: paginatedMessages,
            current_page: page,
            total_items: allMessages.length,
            total_pages: Math.ceil(allMessages.length / perPage)
          },
          pagination: {
            current_page: page,
            total_pages: Math.ceil(allMessages.length / perPage),
            items_per_page: perPage,
            total_items: allMessages.length
          },
          metadata: {
            timestamp: new Date().toISOString(),
            request_id: null,
            api_version: '1.0',
            processing_time_ms: null
          },
          error_code: null
        };
      } catch (fallbackError) {
        console.error('Fallback conversation fetching failed:', fallbackError);

        // Return empty data as last resort
        return {
          success: true,
          status: 'success',
          message: {
            title: 'No Data',
            description: 'No conversations found'
          },
          data: {
            items: [],
            current_page: 1,
            total_items: 0,
            total_pages: 0
          },
          pagination: {
            current_page: 1,
            total_pages: 0,
            items_per_page: params.per_page || 20,
            total_items: 0
          },
          metadata: {
            timestamp: new Date().toISOString(),
            request_id: null,
            api_version: '1.0',
            processing_time_ms: 0
          },
          error_code: null
        };
      }
    }
  }
};
