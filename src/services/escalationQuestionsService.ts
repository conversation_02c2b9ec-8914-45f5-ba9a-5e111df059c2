import api from "./api";

export interface EscalationQuestion {
  id: string;
  name: string;
  lead_id: string;
  franchisor_id: string;
  answer: string[];
  support_status: string;
  is_deleted: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EscalationQuestionFilters {
  lead_id?: string | null;
  franchisor_id?: string | null;
  support_status?: string | null;
  search?: string | null;
  sort_by?: "name" | "support_status" | "created_at" | null;
  sort_order?: "asc" | "desc" | null;
  page?: number;
  size?: number;
}

export const escalationQuestionsService = {
  /**
   * GET /api/questions_Module/escalation-question-bank
   */
  getAllEscalationQuestions: async (filters: EscalationQuestionFilters) => {
    const response = await api.get("/questions_Module/escalation-question-bank", {
      params: filters,
    });
    const details = response.data?.data;
    return {
      items: details?.items || [],
      total_count: details?.total_count || 0,
    };
  },

  /**
   * PUT /api/questions_Module/escalation-question-bank/{escalation_id}/answer
   */
  updateEscalationAnswer: async (escalationId: string, answer: string[]) => {
    const response = await api.put(
      `/questions_Module/escalation-question-bank/${escalationId}/answer`,
      { answer }
    );
    return response.data;
  },

  /**
   * PUT /api/questions_Module/escalation-question-bank/{escalation_id}/status
   */
  updateEscalationStatus: async (escalationId: string, status: string) => {
    const response = await api.put(
      `/questions_Module/escalation-question-bank/${escalationId}/status`,
      { support_status: status }
    );
    return response.data;
  },
};
