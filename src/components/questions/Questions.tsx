// NOTE: In this codebase, 'franchise' and 'brand' refer to the same thing, and so do 'category' and 'industry'.
// The UI displays 'Brand' and 'Industry', but variable/function names may still use 'franchise' and 'category' for legacy reasons..
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Loader2,
  GripVertical,
  Filter,
  PlusIcon,
  Trash2Icon,
  ArrowUp,
  ArrowDown,
  ArrowUpDown,
} from "lucide-react";
import { Question } from "../../types";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
  SortingState,
} from "@tanstack/react-table";
import DeleteConfirmModal from "../common/DeleteConfirmModal";
import { FranchisorDropdown, questionsService } from "../../services/questionsService";
import toast from "react-hot-toast";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { SortableItem } from "./SortableItem";
import Select from "../common/Select";
import Pagination from "../common/Pagination";
import MultiSelect from "../common/MultiSelect";

  const questionTypeOptions = [
    { value: 'multiple_choice', label: 'Multiple Choice' },
    { value: 'text', label: 'Text' },
    { value: 'yes_no', label: 'Yes/No' },
  ];

const Questions: React.FC = () => {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [franchisors, setFranchisors] = useState<FranchisorDropdown[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isActiveFilter, setIsActiveFilter] = useState<boolean | null>(null);
  const [savingFranchisor, setSavingQuestion] = useState(false);
  const [togglingStatus, setTogglingStatus] = useState<string | null>(null);
  const [franchiseFilter, setFranchiseFilter] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [deletingQuestion, setDeletingQuestion] = useState<string | null>(null);
  const [deleteModal, setDeleteModal] = useState<{
    open: boolean;
    id: string | null;
  }>({ open: false, id: null });
  // Pagination states
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [loading, setLoading] = useState({
    table: false,
    page: true,
    actions: false,
  });
  const [totalItems, setTotalItems] = useState(0);
  const [showReorderModal, setShowReorderModal] = useState(false);
  const [reorderQuestions, setReorderQuestions] = useState<Question[]>([]);
  const [isReordering, setIsReordering] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([]);

  // Ref to track if a request is in progress
  const isRequestInProgress = useRef(false);

  // Helper function to get franchisor name
  const getFranchiseName = (franchisorId: string, franchisorName?: string) => {
    // If franchisor_name is already provided in the data, use it
    if (franchisorName && franchisorName.trim()) {
      return franchisorName;
    }

    // Otherwise, look it up from the franchisors array
    if (loading.actions) return "Loading...";
    const franchise = franchisors.find((f) => f.id === franchisorId);
    return franchise?.name ?? "Unknown";
  };

  // Tab and Question Bank states
  const [activeTab, setActiveTab] = useState<'prequalification' | 'questionBank'>('prequalification');
  const [questionBankItems, setQuestionBankItems] = useState<any[]>([]);
  const [questionBankLoading, setQuestionBankLoading] = useState(false);

  useEffect(() => {
      const loadFranchisorsForFilter = async () => {
        setLoading((prev) => ({ ...prev, actions: true }));
        try {
          const franchisorsResponse =
            await questionsService.getFranchisorsDropdown();
          if (franchisorsResponse.success) {
            setFranchisors(franchisorsResponse.data.details.franchisors);
          }
        } catch (error) {
          if (
            typeof error === "object" &&
            error !== null &&
            ("name" in error || "code" in error)
          ) {
            if (
              (error as { name?: string }).name === "AbortError" ||
              (error as { code?: string }).code === "ERR_CANCELED" ||
              (error as { code?: string }).code === "ECONNABORTED"
            ) {
              return;
            }
          }
          console.error("Error loading brands:", error);
          toast.error("Failed to load brands");
        } finally {
          setLoading((prev) => ({ ...prev, actions: false }));
        }
      };

      loadFranchisorsForFilter();
  }, []);

// Fetch questions with filters when filters change
    const fetchQuestions = useCallback(async () => {
      if (isRequestInProgress.current) return;
      try {
        isRequestInProgress.current = true;
        setLoading((prev) => ({ ...prev, table: !loading.page }));
        const sort = sorting[0];

        const filters = {
          page: pagination.pageIndex + 1,
          size: pagination.pageSize,
          search: debouncedSearchTerm || null,
          franchisor_id: franchiseFilter || null,
          is_active: isActiveFilter,
          sortBy: sort?.id || null,       // Which column to sort by
          sortOrder: sort?.desc ? 'desc' : 'asc'
        };
        const response = await questionsService.getList(filters);
        const details = (response.data as any).details;
        if (response.success && details && details.items) {
          type APIQuestion = {
            id: string;
            created_at: string;
            franchisor_id: string;
            franchisor_name: string;
            question_text: string;
            question_type: string;
            is_active: boolean;
            order: number;
            expected_answer: string[];
            category: string;
            score_weight: number;
            qualification_weight: number;
            expected_answer_type: string;
            answer_options: string;
            passing_criteria: string;
            validation_rules: string;
            requires_follow_up: boolean;
            follow_up_logic: string;
            is_required: boolean;
          };
          const items = details.items as APIQuestion[];
          // let items = details.items as APIQuestion[];
          // // Sort by order if present
          // if (items.length > 0 && typeof items[0].order === 'number') {
          //   items = items.slice().sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
          // }
          const mappedQuestions = items.map((q) => ({
            id: q.id,
            text: q.question_text || "",
            type: q.question_type || "",
            is_required: q.is_required ?? false,
            isActive: q.is_active ?? false,
            franchisorId: q.franchisor_id || "",
            franchisor_name: q.franchisor_name || "",
            expected_answer: q.expected_answer || [],
            category: q.category || "",
            score_weight: q.score_weight ?? 0,
            qualification_weight: q.qualification_weight ?? 0,
            expected_answer_type: q.expected_answer_type || "",
            answer_options: (() => {
              try {
                const parsed = JSON.parse(q.answer_options || "[]");
                return Array.isArray(parsed) ? parsed : [];
              } catch {
                return [];
              }
            })(),
            passing_criteria: (() => {
              try {
                const parsed = JSON.parse(q.passing_criteria || "{}");
                return Array.isArray(parsed.qualifying_answers)
                  ? parsed.qualifying_answers
                  : "";
              } catch {
                return "";
              }
            })(),
            validation_rules: q.validation_rules || "",
            requires_follow_up: q.requires_follow_up ?? false,
            follow_up_logic: q.follow_up_logic || "",
          }));

          setQuestions(mappedQuestions);
          setTotalItems(details.total || mappedQuestions.length);
          const totalPages = Math.ceil(
          details.total / pagination.pageSize
        );
        if (pagination.pageIndex >= totalPages && totalPages > 0) {
          setPagination((prev) => ({ ...prev, pageIndex: totalPages - 1 }));
        }
        }
      } catch (error) {
        console.error("Error loading questions:", error);
        toast.error("Failed to load data");
        setQuestions([]);
        setPagination((prev) => ({ ...prev, pageIndex: 0 }));
      } finally {
        setLoading((prev) => ({ ...prev, table: false, page: false }));
        isRequestInProgress.current = false;
      }
}, [
  pagination.pageIndex,
  pagination.pageSize,
  debouncedSearchTerm,
  isActiveFilter,
  franchiseFilter,
  sorting
]);

  // Load filtered/paginated questions
  useEffect(() => {
    fetchQuestions();
  }, [fetchQuestions]);

  // Reload questions when switching to prequalification tab
  // useEffect(() => {
  //   if (activeTab === 'prequalification') {
  //     fetchQuestions();
  //   }
  // }, [activeTab, fetchQuestions]);

  // Debounce search term to prevent multiple API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      if (searchTerm !== debouncedSearchTerm) {
        setPagination((prev) => ({ ...prev, pageIndex: 0 }));
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, debouncedSearchTerm]);


  // Toggle question active status
  const toggleQuestionStatus = useCallback(async (id: string) => {
    try {
      setTogglingStatus(id);
      setLoading((prev) => ({ ...prev, actions: true }));
      
      // Call the toggle status service
      const response = await questionsService.toggleStatus(id);
      
      if (response.success) {
        // Update local state with the new status
        setQuestions(prev =>
          prev.map(q => 
            q.id === id 
              ? { ...q, isActive: response.data.details.is_active } 
              : q
          )
        );
        toast.success(response.message.description);
      } else {
        throw new Error(response.message?.description || 'Failed to toggle status');
      }
    } catch (error) {
      console.error("Error toggling question status:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to toggle question status";
      toast.error(errorMessage);
    } finally {
      setTogglingStatus(null);
      setLoading((prev) => ({ ...prev, actions: false }));
    }
  }, []);

  const handleDelete = useCallback((id: string) => {
    setDeleteModal({ open: true, id });
  }, []);

  const handleEdit = useCallback((question: Question) => {
    setEditingQuestion(question);
    setShowModal(true);
  }, []);
  // Add new question
  const handleAddQuestion = () => {
    setEditingQuestion(null);
    setShowModal(true);
  };

  const handleDeleteConfirm = async() => {
    if (!deleteModal.id) return;
    try {
      setDeletingQuestion(deleteModal.id);
      const success = await questionsService.delete(deleteModal.id);
      if (success) {
        setQuestions((prev) => prev.filter((doc) => doc.id !== deleteModal.id));
        toast.success("Question deleted successfully");
        fetchQuestions();
      }
    } catch (error) {
      console.error("Failed to delete question:", error);
      toast.error("Failed to delete question");
    } finally {
      setDeletingQuestion(null);
      setDeleteModal({ open: false, id: null });
    }
  };

  const handleReorderClick = async () => {
  if (!franchiseFilter) return;
  
  try {
    setIsReordering(true);
    const response = await questionsService.getQuestionByFranchisorId(franchiseFilter);
    if (response.success) {
      const questions = response.data.details.questions.map(q => ({
        id: q.id,
        text: q.question_text,
        type: (q.question_type || "text").toLowerCase() as "qualified" | "non_qualified" ,
        franchisor_name : q.franchisor_name,
        required: false,
        industry: (q.industry || "prequalification").toLowerCase() as "prequalification" | "virtual-agent",
        isActive: q.is_active,
        franchisorId: q.franchisor_id,
        order: q.order
      }));
      setReorderQuestions(questions);
      setShowReorderModal(true);
    }
  } catch (error) {
    console.error("Error fetching questions for reorder:", error);
    toast.error("Failed to load questions for reordering");
  } finally {
    setIsReordering(false);
  }
};

const sensors = useSensors(
  useSensor(PointerSensor),
  useSensor(KeyboardSensor, {
    coordinateGetter: sortableKeyboardCoordinates,
  })
);

const handleDragEnd = (event: DragEndEvent) => {
  const { active, over } = event;
  
  if (active.id !== over?.id) {
    setReorderQuestions((items) => {
      const oldIndex = items.findIndex(item => item.id === active.id);
      const newIndex = items.findIndex(item => item.id === over?.id);
      
      return arrayMove(items, oldIndex, newIndex).map((item, index) => ({
        ...item,
        order: index + 1
      }));
    });
  }
};

const saveQuestionOrder = async () => {
  if (!franchiseFilter || reorderQuestions.length === 0) {
    toast.error("Missing required data for reordering");
    return;
  }
  try {
    setLoading(prev => ({ ...prev, actions: true }));
    const updates = reorderQuestions.map((question, index) => ({
      question_id: question.id,
      order_sequence: index + 1
    }));
    const response = await questionsService.updateQuestionOrder(
      franchiseFilter, // franchisor_id from your state
      updates
    );

    if (response.success) {
      toast.success(response.description || "Question order updated successfully");
      setShowReorderModal(false);
      fetchQuestions();
    } else {
      throw new Error(response.description || "Failed to update question order");
    }
  } catch (error) {
    console.error("Error updating question order:", error);
    toast.error(error instanceof Error ? error.message : "Failed to update question order");
  } finally {
    setLoading(prev => ({ ...prev, actions: false }));
  }
};

// Load question bank from API
const loadQuestionBank = async () => {
  setQuestionBankLoading(true);
  try {
    const response = await questionsService.getQuestionBank();

    if (response.success) {
      setQuestionBankItems(response.data.items);
    }
  } catch (error) {
    console.error('Error loading question bank:', error);
    toast.error('Failed to load question bank');
  } finally {
    setQuestionBankLoading(false);
  }
};

  const clearFilters = useCallback(() => {
    setSearchTerm("");
    setFranchiseFilter("");
    setIsActiveFilter(null);
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    setDebouncedSearchTerm("");
  }, []);

  const columns = useMemo<ColumnDef<Question>[]>(
    () => [
      {
        accessorKey: "text",
        header: "Questions",
        enableSorting: true,
        cell: (info) => (
          <div>
            <div className="font-medium text-gray-900">
              {info.getValue() as string || "Untitled Question"}
            </div>
          </div>
        ),
      },
      {
        accessorKey: "type",
        header: "Type",
        cell: (info) => (
          <span className="capitalize text-gray-700">
            {questionTypeOptions?.find((t) => t.value === info.getValue())?.label || (info.getValue() as string) || " - "}
          </span>
        ),
      },
      {
        accessorKey: "franchisor_name",
        header: "Franchisor Name",
         cell: (info) => {
           const row = info.row.original;
           const franchisorName = getFranchiseName(row.franchisorId || "", info.getValue() as string);
           return (
             <span className="text-gray-700">
               {franchisorName || " - "}
             </span>
           );
         },
      },
      {
        accessorKey: "isActive",
        header: "Status",
        cell: (info) => {
          const isActive = info.getValue() as boolean;
          const id = info.row.original.id;
          const isToggling = togglingStatus === id;
          return (
            <div className="flex items-center space-x-3">
              <button
                onClick={() => toggleQuestionStatus(id)}
                disabled={isToggling}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isActive ? "bg-blue-600" : "bg-gray-200"
                } ${
                  isToggling
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer"
                }`}
                title={isActive ? "Deactivate" : "Activate"}
              >
                {isToggling ? (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Loader2
                      className={`w-4 h-4 animate-spin ${
                        isActive ? "text-white" : "text-gray-900"
                      }`}
                    />
                  </div>
                ) : (
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      isActive ? "translate-x-6" : "translate-x-1"
                    }`}
                  />
                )}
              </button>
            </div>
          );
        },
      },
      {
        header: "Actions",
        id: "actions",
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleEdit(row.original)}
              className="text-blue-600 hover:text-blue-800 p-1"
              title="Edit"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleDelete(row.original.id)}
              disabled={deletingQuestion === row.original.id}
              className="text-red-600 hover:text-red-800 p-1"
              title="Delete"
            >
              {deletingQuestion === row.original.id ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4" />
              )}
            </button>
          </div>
        ),
      },
    ],
    [toggleQuestionStatus, handleEdit, handleDelete, togglingStatus, deletingQuestion]
  );

  const table = useReactTable({
    data: questions,
    columns,
    pageCount: Math.ceil(totalItems / pagination.pageSize),
        state: {
          pagination: {
            pageIndex: pagination.pageIndex,
            pageSize: pagination.pageSize,
          },
          sorting,
        },
        onPaginationChange: setPagination,
        onSortingChange: setSorting,
        getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
    enableSortingRemoval: false,
  });


  // Show loading state for initial data load
  if (loading.page && questions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading questions...</div>
        </div>
      </div>
    );
  }

  const isFilterActive = searchTerm.trim() !== "" || isActiveFilter !== null || franchiseFilter !== "";

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Pre-Qualification Questions Management</h1>
            <p className="text-gray-600 mt-1">
              Manage prequalification questions and question bank
            </p>
          </div>
                      <div className="flex items-center gap-2">
            <button
              onClick={handleReorderClick}
              disabled={!franchiseFilter}
              className={`px-4 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                franchiseFilter
                  ? "bg-green-600 text-white hover:bg-green-700"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              <Loader2
                className={`w-4 h-4 ${
                  isReordering ? "animate-spin" : "hidden"
                }`}
              />
              <span>Reorder</span>
            </button>
            <button
              onClick={handleAddQuestion}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Add Question</span>
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        {/* <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('prequalification')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'prequalification'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Prequalification Questions
            </button>
            <button
              onClick={() => {
                setActiveTab('questionBank');
                loadQuestionBank();
              }}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'questionBank'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Question Bank
            </button>
          </nav>
        </div> */}

        {/* Prequalification Questions Tab */}
          <div className="space-y-6">

            {/* Search and Filters */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search questions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
            {/* <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Categories</option>
              <option value="prequalification">Prequalification</option>
              <option value="virtual-agent">Virtual Agent</option>
            </select> */}
            <div className="w-full md:w-auto flex-1 min-w-[160px]">
              <Select
                value={franchiseFilter}
                onChange={(value) => {
                  setFranchiseFilter(value);
                  setPagination((prev) => ({ ...prev, pageIndex: 0 }));
                }}
                placeholder="Select Franchisor"
                options={[
                  { value: "", label: "All Frachisor" },
                  ...franchisors.map((brand) => ({
                    value: brand.id,
                    label: brand.name,
                  }))
                ]}
              />
            </div>

            {/* Status Filter */}
            <div className="w-full md:w-auto flex-1 min-w-[120px]">

              <Select
                value={isActiveFilter === null ? "" : isActiveFilter.toString()}
                onChange={(value) => {
                    setIsActiveFilter(
                      value === "" ? null : value === "true"
                    );
                    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
                  }}
                placeholder="Select Status"
                options={[
                  { value: "", label: "All Status" },
                  { value: "true", label: "Active" },
                  { value: "false", label: "Inactive" },
                ]}
              />
            </div>

            {/* Clear Filters Button */}
            <div className="w-full md:w-auto flex-none">
              <button
                onClick={clearFilters}
                disabled={!isFilterActive}
                className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 w-full md:w-auto justify-center relative"
              >
                <Filter className="w-4 h-4" />
                <span>Clear Filters</span>

                {isFilterActive && (
                  <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Questions List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          {loading.table ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading questions...</span>
            </div>
          ) : questions.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg">No question found</div>
              <div className="text-gray-400 text-sm mt-1">
                Try adjusting your search or filters
              </div>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    {table?.getHeaderGroups()?.map((headerGroup) => (
                      <tr key={headerGroup.id}>
                        {headerGroup.headers.map((header) => {
                          const isSortable = header.column.id === "text";
                      return (
                          <th
                            key={header.id}
                            onClick={isSortable
                              ? header.column.getToggleSortingHandler()
                              : undefined
                          }
                          className={`text-left py-4 px-6 font-medium text-gray-900 ${
                            isSortable
                              ? "cursor-pointer select-none"
                              : ""
                          }`}
                          >
                            <div className="flex items-center gap-1 space-x-1">
                              {flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                              {isSortable && (
                                <>
                                  {header.column.getIsSorted() === "asc" ? (
                                    <ArrowUp className="w-4 h-4 text-blue-600" />
                                  ) : header.column.getIsSorted() === "desc" ? (
                                    <ArrowDown className="w-4 h-4 text-blue-600" />
                                  ) : (
                                    <ArrowUpDown className="w-4 h-4 text-gray-400" />
                                  )}
                                </>
                              )}
                            </div>
                          </th>
                          )
                        })}
                      </tr>
                    ))}
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {table.getRowModel().rows.map((row) => (
                      <tr key={row.id} className="hover:bg-gray-50">
                        {row.getVisibleCells().map((cell) => (
                          <td key={cell.id} className="py-4 px-6">
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <Pagination
                pageIndex={pagination.pageIndex}
                pageSize={pagination.pageSize}
                totalCount={totalItems}
                pageCount={table.getPageCount()}
                onPageChange={(page) => setPagination((prev) => ({ ...prev, pageIndex: page }))}
                onPrevious={() => table.previousPage()}
                onNext={() => table.nextPage()}
                canPreviousPage={table.getCanPreviousPage()}
                canNextPage={table.getCanNextPage()}
              />
            </>
          )}
        </div>

        {/* Add/Edit Question Modal */}
        {showModal && (
          <QuestionModal
            question={editingQuestion}
            franchisors={franchisors}
            onClose={() => setShowModal(false)}
            onSave={async (formData) => {
              try {
                setSavingQuestion(true);
                setLoading((prev) => ({ ...prev, actions: true }));

                const finalPassingCriteria = Array.isArray(
                  formData.passing_criteria
                )
                  ? JSON.stringify({
                      qualifying_answers: formData.passing_criteria,
                    })
                  : formData.passing_criteria;

                const basePayload = {
                  question_text: formData.text,
                  question_type: formData.type,
                  expected_answer: formData.expected_answer,
                  expected_answer_type: formData.expected_answer_type,
                  answer_options: JSON.stringify(formData.answer_options),
                  score_weight: Number(formData.score_weight),
                  qualification_weight: Number(formData.qualification_weight),
                  passing_criteria: finalPassingCriteria,
                  validation_rules: formData.validation_rules,
                  requires_follow_up: formData.requires_follow_up,
                  follow_up_logic: formData.follow_up_logic,
                  is_required: formData.is_required,
                  category: formData.category,
                };

                if (editingQuestion?.id) {
                  const res = await questionsService.update(
                    editingQuestion.id,
                    basePayload
                  );
                  if(res.success){
                    toast.success(res.message?.title || "Question updated successfully");
                  }
                } else {
                  // Add new question
                  const createPayload = {
                    franchisor_id: formData.franchisorId || "",
                    ...basePayload
                  };
                  const res = await questionsService.create(createPayload);
                  if(res.success){                    
                    toast.success(res.message?.title || "Question added successfully");
                  }
                }
                setShowModal(false);
                setEditingQuestion(null);
                fetchQuestions();
              } catch {
                toast.error("Failed to save question");
              } finally {
                setSavingQuestion(false);
                setLoading((prev) => ({ ...prev, actions: false }));
              }
            }}
            saving={savingFranchisor}
          />
        )}

          </div>
        {/* {activeTab === 'prequalification' && (
        )} */}
          {/* <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Virtual Agent Exceptions
            </h2>
            <button
              onClick={() => {
                // Pre-fill a new question with virtual-agent category
                setEditingQuestion({
                  id: "",
                  text: "",
                  type: "qualified",
                  required: true,
                  category: "virtual-agent",
                  isActive: true,
                });
                setShowModal(true);
              }}
              className="bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Add Exception Question</span>
            </button>
          </div> */}

          {/* <div className="space-y-4">
            <div className="p-4 bg-yellow-50 rounded-lg flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm text-gray-700">
                  Virtual Agent Exceptions are questions that need to be
                  answered when the AI cannot handle a specific query. These
                  questions are added to the question bank and can be marked as
                  answered once resolved.
                </p>
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <h3 className="font-medium text-gray-900">Recent Exceptions</h3>
              </div>

              <div className="divide-y divide-gray-200">
                <div className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">
                        What are the specific territory restrictions for Pizza
                        Express?
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        Added: 2 days ago
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        className="text-green-600 hover:text-green-800 p-1"
                        title="Mark as Answered"
                      >
                        <Check className="w-4 h-4" />
                      </button>
                      <button
                        className="text-blue-600 hover:text-blue-800 p-1"
                        title="Add to Question Bank"
                        onClick={() => {
                          setEditingQuestion({
                            id: "",
                            text: "What are the specific territory restrictions for Pizza Express?",
                            type: "qualified",
                            required: true,
                            category: "virtual-agent",
                            isActive: true,
                          });
                          setShowModal(true);
                        }}
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">
                        How does Fitness First handle equipment leasing?
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        Added: 3 days ago
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        className="text-green-600 hover:text-green-800 p-1"
                        title="Mark as Answered"
                      >
                        <Check className="w-4 h-4" />
                      </button>
                      <button
                        className="text-blue-600 hover:text-blue-800 p-1"
                        title="Add to Question Bank"
                        onClick={() => {
                          setEditingQuestion({
                            id: "",
                            text: "How does Fitness First handle equipment leasing?",
                            type: "qualified",
                            required: true,
                            category: "virtual-agent",
                            isActive: true,
                          });
                          setShowModal(true);
                        }}
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="p-4 hover:bg-gray-50 bg-gray-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900 line-through">
                        What training is provided for new franchisees?
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        Added: 5 days ago • Answered
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        Answered
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div> */}


        {/* Question Bank Tab */}
        {/* {activeTab === 'questionBank' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Question Bank</h2>
            </div>

            <div className="mb-4 text-sm text-gray-500">
              <p>Question bank contains reusable questions available in the system.</p>
            </div>

            {questionBankLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading question bank...</span>
              </div>
            ) : (
              <div className="overflow-hidden border border-gray-200 rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Question Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created At
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {questionBankItems.length > 0 ? (
                      questionBankItems.map((item) => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{item.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              item.is_active
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {item.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(item.created_at).toLocaleDateString()}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={3} className="px-6 py-12 text-center">
                          <div className="text-gray-500">
                            <AlertCircle className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                            <h3 className="text-lg font-medium text-gray-900 mb-1">No Questions Available</h3>
                            <p className="text-gray-500">No questions found in the question bank.</p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )} */}
      </div>
      <DeleteConfirmModal
        open={deleteModal.open}
        title="Delete Question"
        message="Are you sure you want to delete this question? This action cannot be undone."
        onCancel={() => setDeleteModal({ open: false, id: null })}
        onConfirm={handleDeleteConfirm}
        loading={!!deletingQuestion}
      />

      {showReorderModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Reorder Questions
              </h2>
            </div>

            {reorderQuestions.length === 0 ? (
              <>
                <div className="p-4 text-gray-500 text-center">
                  <p>No questions available for reordering.</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Try selecting a different brand or add new questions to this brand first.<br />
                    Only questions belonging to the selected brand can be reordered.
                  </p>
                  <div className="mt-4 flex justify-end">
                    <button
                      onClick={() => setShowReorderModal(false)}
                      className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                    >
                      OK
                    </button>
                  </div>
                </div>
              </>
            ) : (

            <div className="p-6 space-y-4">
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
                modifiers={[restrictToVerticalAxis]}
              >
                <SortableContext
                  items={reorderQuestions}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-3">
                    {reorderQuestions.map((question, index) => (
                      <SortableItem key={question.id} id={question.id}>
                        {({ dragAttributes, dragListeners }: any) => (
                          <div className="p-3 border border-gray-200 rounded-lg flex items-center justify-between bg-white">
                            <div className="flex items-center space-x-3">
                              <span className="text-gray-500 w-6 text-center">
                                {index + 1}.
                              </span>
                              <span className="font-medium">{question.text}</span>
                            </div>
                            <div className="flex space-x-2">
                              <button
                                className="p-1 text-gray-500 hover:text-gray-700 disabled:opacity-30 cursor-grab active:cursor-grabbing"
                                {...dragAttributes}
                                {...dragListeners}
                              >
                                <GripVertical size={20} />
                              </button>
                            </div>
                          </div>
                        )}
                      </SortableItem>
                    ))}
                  </div>
                </SortableContext>
              </DndContext>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  onClick={() => setShowReorderModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={saveQuestionOrder}
                  disabled={loading.actions}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  {loading.actions ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin inline mr-2" />
                      <span>Saving...</span>
                    </>
                  ) : <span>Save Order</span>}
                </button>
              </div>
            </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

interface QuestionModalProps {
  question: Question | null;
  franchisors: FranchisorDropdown[];
  onClose: () => void;
  onSave: (question: Question) => void;
  saving: boolean;
}

const QuestionModal: React.FC<QuestionModalProps>  = ({
  question,
  franchisors,
  onClose,
  onSave,
  saving,
}) => {
  const isAddMode = !question?.id;
  const [formData, setFormData] = useState<Question>({
    id: question?.id || "",
    text: question?.text || "",
    type: question?.type || "text",
    isActive: question?.isActive ?? true,
    franchisorId: question?.franchisorId || "",
    expected_answer: question?.expected_answer || [],
    expected_answer_type: question?.expected_answer_type || "",
    answer_options: question?.answer_options || [],
    score_weight: question?.score_weight ?? 0,
    qualification_weight: question?.qualification_weight ?? 0,
    passing_criteria: question?.passing_criteria || "",
    validation_rules: question?.validation_rules || "",
    requires_follow_up: question?.requires_follow_up ?? false,
    follow_up_logic: question?.follow_up_logic || "",
    is_required: question?.is_required ?? false,
    category: question?.category || "",
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.text.trim()) newErrors.text = "Question text is required.";
    if (!formData.type) newErrors.type = "Question type is required.";
    if (!formData.expected_answer_type)
      newErrors.expected_answer_type = "Expected answer type is required.";
    if (!formData.franchisorId) newErrors.franchisorId = "Franchisor is required.";
    if (!formData.category.trim()) newErrors.category = "Category is required.";
    if (
      !Array.isArray(formData.expected_answer) ||
      formData.expected_answer.length === 0
    )
      newErrors.expected_answer = "At least one expected answer is required.";
    if (
      !Array.isArray(formData.answer_options) ||
      formData.answer_options.length === 0
    )
      newErrors.answer_options = "At least one answer option is required.";
    if (formData.score_weight === undefined || formData.score_weight < 0)
      newErrors.score_weight = "Score weight is required.";
    if (
      formData.qualification_weight === undefined ||
      formData.qualification_weight < 0
    )
      newErrors.qualification_weight = "Qualification weight is required.";

    if (formData.type === "multiple_choice" || formData.type === "yes_no") {
      if (
        !Array.isArray(formData.passing_criteria) ||
        formData.passing_criteria.length === 0
      ) {
        newErrors.passing_criteria = "Select at least one passing criteria.";
      }
    } else {
      if (
        !formData.passing_criteria ||
        typeof formData.passing_criteria !== "string"
      ) {
        newErrors.passing_criteria = "Passing criteria is required.";
      }
    }
    return newErrors;
  };


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    setErrors({});
    onSave(formData);
  };
  const updateArrayField = (field: string, index: number, value: string) => {
    const updated = [...(formData[field] || [])];
    updated[index] = value;
    setFormData({ ...formData, [field]: updated });
  };

  const removeArrayField = (field: string, index: number) => {
    const updated = formData[field].filter((_: string, i: number) => i !== index);
    setFormData({ ...formData, [field]: updated });
  };

  const addArrayItem = (field: string) => {
    setFormData({ ...formData, [field]: [...(formData[field] || []), ""] });
  };

  const isCriteriaSelect =
    formData.type === "multiple_choice" ||
    formData.type === "yes_no";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {isAddMode ? "Add New Question" : "Edit Question"}
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Question Text */}
            <div className="col-span-full">
              <label className="block text-sm font-medium mb-1">Question Text *</label>
              <textarea
                value={formData.text}
                onChange={(e) => setFormData({ ...formData, text: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              />
              {errors.text && <p className="text-red-500 text-sm mt-1">{errors.text}</p>}
            </div>

            {/* Question Type */}
            <div>
              <label className="block text-sm font-medium mb-1">Question Type *</label>
              <Select
                value={formData.type}
                placeholder="Select Question Type"
                onChange={(val) => {
                  setFormData({ ...formData, type: val, passing_criteria: "" });
                }}
                options={questionTypeOptions}
              />
              {errors.text && <p className="text-red-500 text-sm mt-1">{errors.type}</p>}
            </div>

            {/* Expected Answer Type */}
            <div>
              <label className="block text-sm font-medium mb-1">Expected Answer Type *</label>
              <Select
                value={formData.expected_answer_type}
                placeholder="Select Expected Answer Type"
                onChange={(val) => setFormData({ ...formData, expected_answer_type: val })}
                options={[
                  { value: "text", label: "Text" },
                  { value: "choice", label: "Choice" },
                  { value: "boolean", label: "Boolean" },
                ]}
              />
              {errors.expected_answer_type && <p className="text-red-500 text-sm mt-1">{errors.expected_answer_type}</p>}
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Category *
              </label>
              <input
                type="text"
                value={formData.category}
                onChange={(e) =>
                  setFormData({ ...formData, category: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              />
              {errors.category && <p className="text-red-500 text-sm mt-1">{errors.category}</p>}
            </div>

            {/* Score Weight */}
            <div>
              <label className="block text-sm font-medium mb-1">Score Weight *</label>
              <input
                type="number"
                value={formData.score_weight}
                min={0}
                step="0.1"
                placeholder="Enter score weight (e.g., 1.0)"
                onChange={(e) => {
                  const value = e.target.value;
                  setFormData({ ...formData, score_weight: value === '' ? 0 : parseFloat(value) || 0 });
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {errors.score_weight && <p className="text-red-500 text-sm mt-1">{errors.score_weight}</p>}
            </div>

            {/* Qualification Weight */}
            <div>
              <label className="block text-sm font-medium mb-1">Qualification Weight *</label>
              <input
                type="number"
                min={0}
                step="0.1"
                value={formData.qualification_weight}
                placeholder="Enter qualification weight (e.g., 1.0)"
                onChange={(e) => {
                  const value = e.target.value;
                  setFormData({ ...formData, qualification_weight: value === '' ? 0 : parseFloat(value) || 0 });
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {errors.qualification_weight && <p className="text-red-500 text-sm mt-1">{errors.qualification_weight}</p>}
            </div>

            {/* Validation Rules */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Validation Rules
              </label>
              <input
                type="text"
                value={formData.validation_rules}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    validation_rules: e.target.value,
                  })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              />
            </div>

            {/* Follow Up Logic */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Follow Up Logic
              </label>
              <input
                type="text"
                value={formData.follow_up_logic}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    follow_up_logic: e.target.value,
                  })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              />
            </div>

            {/* Required + Follow-up checkboxes */}
            <div className="flex items-center gap-4">
              <label className="flex items-center text-sm gap-2">
                <input
                  type="checkbox"
                  checked={formData.is_required}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      is_required: e.target.checked,
                    })
                  }
                />
                Required
              </label>

              <label className="flex items-center text-sm gap-2">
                <input
                  type="checkbox"
                  checked={formData.requires_follow_up}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      requires_follow_up: e.target.checked,
                    })
                  }
                />
                Requires Follow Up *
              </label>
            </div>
          </div>

          {/* Franchisor Select */}
          {isAddMode && (
            <div>
              <label className="block text-sm font-medium mb-1">
                Franchisor *
              </label>
              <Select
                value={formData.franchisorId as string}
                placeholder="Select a franchisor"
                onChange={(val) => setFormData({ ...formData, franchisorId: val })} 
                options={franchisors.map((f) => ({ value: f.id, label: f.name }))}
              />
              {errors.franchisorId && <p className="text-red-500 text-sm mt-1">{errors.franchisorId}</p>}
            </div>
          )}

          {/* Expected Answer List */}
          <div>
            <label className="block text-sm font-medium mb-1">Answers *</label>
            <div className="space-y-2 max-h-36 overflow-y-auto border rounded-lg p-2">
              {formData.expected_answer?.map((answer, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={answer}
                    onChange={(e) => updateArrayField("expected_answer", index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => removeArrayField("expected_answer", index)}
                    className="text-red-500"
                  >
                    <Trash2Icon className="w-4 h-4" />
                  </button>
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayItem("expected_answer")}
                className="text-blue-600 text-sm flex gap-2 items-center"
              >
                <PlusIcon className="w-4 h-4" /> Add Answer
              </button>
            </div>
            {errors.expected_answer && <p className="text-red-500 text-sm mt-1">{errors.expected_answer}</p>}
          </div>

          {/* Answer Options (Same as Answers) */}
          <div>
            <label className="block text-sm font-medium mb-1">Answer Options *</label>
            <div className="space-y-2 max-h-36 overflow-y-auto border rounded-lg p-2">
              {formData.answer_options.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={option}
                    onChange={(e) => updateArrayField("answer_options", index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => removeArrayField("answer_options", index)}
                    className="text-red-500"
                  >
                    <Trash2Icon className="w-4 h-4" />
                  </button>
                </div>
              ))}
              <button
                type="button"
                onClick={() => addArrayItem("answer_options")}
                className="text-blue-600 text-sm flex gap-2 items-center"
              >
                <PlusIcon className="w-4 h-4" /> Add Option
              </button>
            </div>
            {errors.answer_options && <p className="text-red-500 text-sm mt-1">{errors.answer_options}</p>}
          </div>

          {/* Follow-up, Validation, etc */}
          <div className="pb-8">
            <label className="block text-sm font-medium mb-1">
              Passing Criteria *
            </label>
            {isCriteriaSelect ? (
              <MultiSelect
                options={(formData.expected_answer || []).map((val) => ({ label: val, value: val }))}
                value={Array.isArray(formData.passing_criteria) ? formData.passing_criteria : []}
                onChange={(selected) =>
                  setFormData({ ...formData, passing_criteria: selected })
                }
              />
            ) : (
              <input
                type="text"
                value={formData.passing_criteria}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    passing_criteria: e.target.value,
                  })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              />
            )}
            {errors.passing_criteria && <p className="text-red-500 text-sm mt-1">{errors.passing_criteria}</p>}
          </div>

          <div className="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
            >
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>{isAddMode ? "Creating..." : "Updating..."}</span>
                </>
              ) : (
                <span>{isAddMode ? "Add Question" : "Update Question"}</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Questions;
