import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Mail, Lock, Eye, EyeOff, Phone } from 'lucide-react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import Logo from "../../assets/logo.png";
import toast from 'react-hot-toast';

// Dynamic validation schema based on login type
const getLoginSchema = (loginType: 'email' | 'phone') => {
  return Yup.object().shape({
    identifier: loginType === 'email'
      ? Yup.string()
          .required('Email is required')
          .email('Please enter a valid email address')
      : Yup.string()
          .required('Phone number is required')
          .test('phone-validation', 'Please enter a valid phone number', function(value) {
            if (!value) return false;

            // Remove all non-digit characters for validation
            const cleanNumber = value.replace(/\D/g, '');

            // Australian mobile numbers (with or without country code)
            // Format: 04XXXXXXXX (10 digits) or +61 4XXXXXXXX (11 digits after +61)
            const australianMobile = /^(04\d{8}|614\d{8})$/;

            // Australian landline numbers (with or without country code)
            // Format: 0[2-9]XXXXXXXX (10 digits) or +61 [2-9]XXXXXXXX
            const australianLandline = /^(0[2-9]\d{8}|61[2-9]\d{8})$/;

            // International format: + followed by 1-3 digit country code and 4-14 digits
            const international = /^(\+\d{1,3}\d{4,14})$/;

            // Check if it matches Australian formats (cleaned number)
            if (australianMobile.test(cleanNumber) || australianLandline.test(cleanNumber)) {
              return true;
            }

            // Check if it matches international format (original value with +)
            if (value.startsWith('+') && international.test(value)) {
              return true;
            }

            // Check for common international formats without + (US, UK, etc.)
            // US: 10 digits, UK: 11 digits, etc.
            if (cleanNumber.length >= 7 && cleanNumber.length <= 15) {
              return true;
            }

            return false;
          }),
    password: Yup.string()
      .required('Password is required')
      .min(6, 'Password must be at least 6 characters'),
    remember: Yup.boolean()
  });
};

const Login: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loginType, setLoginType] = useState<'email' | 'phone'>('email');
  const { login } = useAuth();
  const navigate = useNavigate();
  localStorage.removeItem('forgot_password_email');

  const handleSubmit = async (values: { identifier: string; password: string; remember: boolean }) => {
    setIsLoading(true);
    try {
      const success = await login(values.identifier, values.password, values.remember);
      if (success) {
        navigate('/analytics');
      } else {
        toast.error('Invalid credentials.');
      }
    } catch (err: any) {
      toast.error(err?.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="text-center mb-8">
            <img src={Logo} className="h-10 mb-8 mt-2 m-auto" alt="GrowthHive Logo" />
            <h1 className="text-2xl font-bold text-gray-900">Welcome Back</h1>
            <p className="text-gray-600 mt-2">Sign in to your GrowthHive admin panel</p>
          </div>

          <Formik
            initialValues={{ identifier: '', password: '', remember: true }}
            validationSchema={getLoginSchema(loginType)}
            onSubmit={handleSubmit}
          >
            {({ isSubmitting, setFieldValue, setFieldError, setFieldTouched }) => {
              const handleToggle = (newType: 'email' | 'phone') => {
                setLoginType(newType);
                // Clear both identifier and password fields and their error/touched states
                setFieldValue('identifier', '');
                setFieldValue('password', '');
                setFieldError('identifier', undefined);
                setFieldError('password', undefined);
                setFieldTouched('identifier', false);
                setFieldTouched('password', false);
              };

              return (
                <Form className="space-y-6">
                  {/* Login Type Toggle */}
                  <div className="flex items-center justify-center mb-6">
                    <div className="bg-gray-100 p-1 rounded-lg flex">
                      <button
                        type="button"
                        onClick={() => handleToggle('email')}
                        className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                          loginType === 'email'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        <Mail className="w-4 h-4 inline mr-2" />
                        Email
                      </button>
                      <button
                        type="button"
                        onClick={() => handleToggle('phone')}
                        className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                          loginType === 'phone'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        <Phone className="w-4 h-4 inline mr-2" />
                        Phone
                      </button>
                    </div>
                  </div>

                <div>
                  <label htmlFor="identifier" className="block text-sm font-medium text-gray-700 mb-2">
                    {loginType === 'email' ? 'Email Address' : 'Phone Number (Australian or International)'}
                  </label>
                  <div className="relative">
                    {loginType === 'email' ? (
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    ) : (
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    )}
                    <Field
                      id="identifier"
                      name="identifier"
                      type={loginType === 'email' ? 'email' : 'tel'}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      placeholder={
                        loginType === 'email'
                          ? 'Enter your email address'
                          : 'Enter phone number (e.g., 0412345678, +***********)'
                      }
                    />
                  </div>
                  <ErrorMessage name="identifier" component="div" className="text-red-500 text-sm mt-1" />
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                    Password
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Field
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      placeholder="Enter your password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                  <ErrorMessage name="password" component="div" className="text-red-500 text-sm mt-1" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Field
                      id="remember"
                      name="remember"
                      type="checkbox"
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="remember" className="ml-2 text-sm text-gray-600">
                      Remember me
                    </label>
                  </div>
                  <button
                    type="button"
                    onClick={() => navigate("/forgot-password")}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Forgot Password?
                  </button>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting || isLoading}
                  className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 px-4 rounded-lg font-medium hover:from-yellow-700 hover:to-yellow-700 focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Signing In...' : 'Login'}
                </button>
              </Form>
              );
            }}
          </Formik>
        </div>
      </div>
    </div>
    </>
  );
};

export default Login;
