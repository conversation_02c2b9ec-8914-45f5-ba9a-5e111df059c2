export function getVisiblePages(currentPage: number, totalPages: number): (number | "...")[] {
  const delta = 1; // number of pages before & after current
  const range: (number | "...")[] = [];

  const left = Math.max(currentPage - delta, 2);
  const right = Math.min(currentPage + delta, totalPages - 1);

  range.push(1); // always show first page

  if (left > 2) range.push("...");

  for (let i = left; i <= right; i++) {
    range.push(i);
  }

  if (right < totalPages - 1) range.push("...");

  if (totalPages > 1) range.push(totalPages); // always show last page

  return range;
}
