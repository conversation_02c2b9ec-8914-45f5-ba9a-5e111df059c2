import React, { useEffect, useState } from "react";
import { Loader2, Save, Edit } from "lucide-react";
import toast from "react-hot-toast";
import { salesScriptsServices, Script } from "../../services/salesScriptsServices";

const SalesScripts: React.FC = () => {

  const [scripts, setScripts] = useState<Script[]>([]);
  const [loading, setLoading] = useState({page:true, action: false});
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState<Record<string, string>>({});

    useEffect(() => {
    const fetchScripts = async () => {
      try {
        const res = await salesScriptsServices.getSalesScripts();
        // Sort scripts by order_sequence in ascending order
        const sortedScripts = (res.items || []).sort((a, b) => a.order_sequence - b.order_sequence);
        setScripts(sortedScripts);
      } catch (err) {
        console.error(err);
        toast.error(err?.response?.data.message.description || 'Failed to load scripts');
      } finally {
        setLoading((prev) => ({ ...prev, page: false }));
      }
    };
    fetchScripts();
  }, []);

  const handleSave = async (script: Script) => {
    const newContent = editedContent[script.id] ?? script.script_content;
    setLoading((prev) => ({ ...prev, action: true }));
    try {
      const res = await salesScriptsServices.updateSalesScript(script.id, {
        ...script,
        script_content: newContent,
      });
      if(res.success){
        toast.success(res.message.title || 'Script updated');
        setEditingId(null);
        setScripts(prev =>
          prev.map(s => (s.id === script.id ? { ...s, script_content: newContent } : s))
        );
      }
    } catch (err) {
      console.error(err);
      toast.error(err?.response?.data.message.description || 'Failed to update script');
    } finally {
      setLoading((prev) => ({ ...prev, action: false }));
    }
  };

    if (loading.page) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="animate-spin w-6 h-6 text-blue-500" />
        <span className="ml-2 text-gray-600">Loading sales scripts...</span>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Sales Script Management
            </h1>
            <p className="text-gray-600 mt-1">
              Create and manage sales scripts for your franchises
            </p>
          </div>
        </div>

        {scripts.map((script) => {
          const isEditing = editingId === script.id;
          const stageLabel = script.script_stage.replace("_", " ");

          return (
            <div
              key={script.id}
              className="p-6 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow space-y-4"
            >
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div>
                  <div className="flex items-center gap-3">
                    <span className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 text-sm font-semibold rounded-full">
                      {script.order_sequence}
                    </span>
                    <h2 className="text-xl font-semibold text-gray-900">
                      {script.script_title}
                    </h2>
                  </div>
                  <span className="inline-block mt-1 ml-11 px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600 capitalize">
                    {stageLabel}
                  </span>
                </div>

                <div>
                  {isEditing ? (
                    <button
                      onClick={() => handleSave(script)}
                      className="flex items-center gap-2 px-4 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition disabled:opacity-50"
                      disabled={loading.action}
                    >
                      {loading.action && (
                        <Loader2 className="animate-spin w-4 h-4" />
                      )}
                      <Save className="w-4 h-4" />
                      {loading.action ? "Saving..." : "Save"}
                    </button>
                  ) : (
                    <button
                      onClick={() => {
                        setEditingId(script.id);
                        setEditedContent((prev) => ({
                          ...prev,
                          [script.id]: script.script_content,
                        }));
                      }}
                      className="flex items-center gap-2 px-4 py-1.5 text-sm border border-gray-300 hover:border-blue-600 text-gray-700 rounded-lg hover:text-blue-600 transition"
                    >
                      <Edit className="w-4 h-4" />
                      Edit
                    </button>
                  )}
                </div>
              </div>

              <div>
                {isEditing ? (
                  <textarea
                    value={editedContent[script.id]}
                    onChange={(e) =>
                      setEditedContent((prev) => ({
                        ...prev,
                        [script.id]: e.target.value,
                      }))
                    }
                    className="w-full px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[120px] resize-none"
                  />
                ) : (
                  <p className="text-gray-800 text-sm whitespace-pre-wrap leading-relaxed">
                    {script.script_content}
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default SalesScripts;
