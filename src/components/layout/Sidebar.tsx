import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  Building2, 
  Users, 
  FileText, 
  MessageSquare, 
  Calendar, 
  BarChart3, 
  Settings, 
  HelpCircle,
  Home,
  Tag,
  Layers,
  AlertCircle,
  MessageCircleQuestion
} from 'lucide-react';
import Logo from "../../assets/logo.png"

const Sidebar: React.FC = () => {
  const menuItems = [
    // { icon: Home, label: 'Dashboard', path: '/dashboard' },
    { icon: BarChart3, label: 'Analytics', path: '/analytics' },
    { icon: Building2, label: 'Franchisors', path: '/franchisors' },
    { icon: Users, label: 'Leads', path: '/leads' },

    { icon: Tag, label: 'Industries', path: '/industries' },
    // { icon: Tag, label: 'Categories', path: '/categories' },
    // { icon: Layers, label: 'Subcategories', path: '/subcategories' },
    { icon: FileText, label: 'Documents', path: '/documents' },
    { icon: FileText, label: 'Sales Scripts', path: '/scripts' },
    { icon: HelpCircle, label: 'Prequalification Questions', path: '/prequalification-questions' },
    { icon: Calendar, label: 'Meeting Room', path: '/meetings' },
    { icon: MessageCircleQuestion, label: 'Question Bank', path: '/question-bank' },
    { icon: AlertCircle, label: 'Escalation Questions', path: '/escalation-questions' },
    { icon: Settings, label: 'Settings', path: '/settings' },
  ];

  return (
    <div className="h-full bg-white shadow-lg border-r border-gray-200">
      <div className="p-6">
        <div className="flex items-center space-x-3">
          {/* <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
            <Building2 className="w-6 h-6 text-white" />
          </div> */}
          <div>
            {/* <h1 className="text-xl font-bold text-gray-900">GrowthHive</h1>
            <p className="text-sm text-gray-500">Management Panel</p> */}
            <img src={Logo}/>
          </div>
        </div>
      </div>
      
      <nav className="px-4 pb-6">
        <ul className="space-y-2">
          {menuItems.map((item) => (
            <li key={item.path}>
              <NavLink
                to={item.path}
                className={({ isActive }) =>
                  `flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                    isActive
                      ? 'bg-yellow-50 text-yellow-700 border-r-2 border-yellow-500'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`
                }
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.label}
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar;
