import api from "./api";

// Franchisor interfaces matching your API response
export interface CategoryDetails {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
}

export interface SubcategoryDetails {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  is_deleted: boolean;
  category_id: string;
  created_at: string;
  updated_at: string;
}

export interface Franchisor {
  id: string;
  name: string;
  contactFirstName: string | null;
  contactLastName: string | null;
  industry_id: string;
  industry_details: CategoryDetails | null;
  region: string;
  budget: number;
  brochure_url?: string | null;
  email: string | null;
  phone: string | null;
  franchisor_won_id?: string | null;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
}

export interface Pagination {
  current_page: number;
  total_pages: number;
  items_per_page: number;
  total_items: number;
}

export interface FranchisorsData {
  items: Franchisor[];
  total_count: number;
  pagination: Pagination;
}

export interface FranchisorsResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: FranchisorsData;
  metadata: {
    timestamp: string;
    request_id: string | null;
    api_version: string;
    processing_time_ms: number | null;
  };
  error_code: string | null;
}

// Filter parameters for franchisors API
export interface FranchisorsFilters {
  skip: number;
  limit: number;
  industry?: string | null;
  region?: string | null;
  is_active?: boolean | null;
  search?: string | null;
  sort_by?: string | null;
  sort_order?: string | null;
}

// Category interface for dropdown
export interface CategoryOption {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
}

export interface CategoriesResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: CategoryOption[];
}

// Subcategory interface for dropdown
export interface SubcategoryOption {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  is_deleted: boolean;
  category_id: string;
  created_at: string;
  updated_at: string;
}

export interface SubcategoriesResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: SubcategoryOption[];
}


// Interface for import error details
interface ImportError {
  row: number;
  field: string;
  message: string;
}

// Interface for CSV import response
export interface ImportCsvResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: {
    total_rows: number;
    successful_imports: number;
    failed_imports: number;
    errors: ImportError[];
  };
}

// Interface for brochure upload response
export interface UploadBrochureResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: {
    id: string;
    name: string;
    industry: string;
    region: string;
    budget: number;
    sub_category: string;
    brochure_url: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
}

export const franchisorsService = {
  /**
   * Get list of franchisors with filters and pagination
   * Route: GET /franchisors
   * Required params: skip, limit
   * Optional params: industry, region, is_active, search, sort_by, sort_order
   */
  getList: async (
    filters: FranchisorsFilters
  ): Promise<FranchisorsResponse> => {
    try {
      const params = new URLSearchParams();

      // Required parameters
      params.append("skip", filters.skip.toString());
      params.append("limit", filters.limit.toString());

      // Optional parameters
      if (
        filters.industry !== null &&
        filters.industry !== undefined &&
        filters.industry.trim() !== ""
      ) {
        params.append("industry", filters.industry.trim());
      }

      if (
        filters.region !== null &&
        filters.region !== undefined &&
        filters.region.trim() !== ""
      ) {
        params.append("region", filters.region.trim());
      }

      if (filters.is_active !== null && filters.is_active !== undefined) {
        params.append("is_active", filters.is_active.toString());
      }

      if (
        filters.search !== null &&
        filters.search !== undefined &&
        filters.search.trim() !== ""
      ) {
        params.append("search", filters.search.trim());
      }

      if (
        filters.sort_by !== null &&
        filters.sort_by !== undefined &&
        filters.sort_by.trim() !== ""
      ) {
        params.append("sort_by", filters.sort_by.trim());
      }

      if (
        filters.sort_order !== null &&
        filters.sort_order !== undefined &&
        filters.sort_order.trim() !== ""
      ) {
        params.append("sort_order", filters.sort_order.trim());
      }

      const url = `franchisors/?${params.toString()}`;

      const response = await api.get<FranchisorsResponse>(url);
      return response.data;
    } catch (error) {
      console.error("Error in franchisorsService.getList:", error);
      throw error;
    }
  },

  /**
   * Get a single franchisor by ID
   */
  getById: async (id: string): Promise<Franchisor> => {
    const response = await api.get<{ data: Franchisor }>(`franchisors/${id}`);
    return response.data.data;
  },

  /**
   * Create a new franchisor
   */
  create: async (data: Partial<Franchisor>): Promise<Franchisor> => {
    const { brochure_url, ...creationData } = data;
    const response = await api.post<{ data: Franchisor }>("/franchisors/", creationData);
    console.log("Response from create franchisor:", response.data);
    return response.data.data;
  },

  /**
   * Update an existing franchisor
   */
  update: async (id: string, data: Partial<Franchisor>): Promise<boolean> => {
    const formData = new FormData();
    
    // Append all fields to FormData
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, String(value));
      }
    });

    const response = await api.put<{
      success: boolean;
      status: string;
      message: {
        title: string;
        description: string;
      }
    }>(`franchisors/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data.success;
  },

  /**
   * Delete a franchisor
   */
  delete: async (id: string): Promise<boolean> => {
    const response = await api.delete<{ success: boolean }>(
      `franchisors/${id}`
    );
    return response.data.success;
  },

  /**
   * Get industries for dropdown filter
   * Route: GET /franchisors/industries
   */
  getCategories: async (): Promise<CategoryOption[]> => {
    try {
      const response = await api.get<CategoriesResponse>(
        "franchisors/industries"
      );

      if (response.data.success) {
        return response.data.data;
      } else {
        console.error("Failed to fetch categories:", response.data.message);
        return [];
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      return [];
    }
  },

  /**
   * Get subcategories for dropdown filter
   * Route: GET /franchisors/subcategories
   * Optional: Can filter by category_id
   */
  getSubcategories: async (
    categoryId?: string
  ): Promise<SubcategoryOption[]> => {
    try {
      const url = `franchisors/industries/${categoryId}/subcategories`;

      const response = await api.get<SubcategoriesResponse>(url);

      if (response.data.success) {
        return response.data.data;
      } else {
        console.error("Failed to fetch subcategories:", response.data.message);
        return [];
      }
    } catch (error) {
      console.error("Error fetching subcategories:", error);
      return [];
    }
  },

  /**
   * Get franchisors for dropdown filter
   * Route: GET /franchisors
   * Optional: Can filter by category_id
   */
    // Add this to your franchisorService object
  importFromCsv: async (file: File): Promise<ImportCsvResponse["data"]> => {
    const formData = new FormData();
    formData.append("file", file);

    const response = await api.post<ImportCsvResponse>(
      "/franchisors/import",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data.data;
  },

  /**
   * Upload a brochure for a franchisor
   * Route: POST /franchisors/:franchisorId/upload-brochure
   */
  uploadBrochure: async (
    franchisorId: string,
    file: File
  ): Promise<UploadBrochureResponse["data"]> => {
    const formData = new FormData();
    formData.append("file", file);

    const response = await api.post<UploadBrochureResponse>(
      `/franchisors/${franchisorId}/upload-brochure`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response.data.data;
  },

  /**
   * Toggle franchisor active status
   * Route: PATCH /franchisors/:franchisorId/activate or /franchisors/:franchisorId/deactivate
   */
  toggleStatus: async (franchisorId: string, setActive: boolean): Promise<boolean> => {
    const endpoint = setActive ? 'activate' : 'deactivate';
    const response = await api.patch<{
      success: boolean;
      status: string;
      message: {
        title: string;
        description: string;
      }
    }>(`franchisors/${franchisorId}/${endpoint}`);

    return response.data.success;
  },

  /**
   * Sync franchisors with Zoho CRM
   * Route: POST /zoho/sync
   */
  syncWithZoho: async (): Promise<{
    success: boolean;
    status: string;
    message: {
      title: string;
      description: string;
    };
    data: {
      franchisors_pulled: number;
      franchisors_pushed: number;
      franchisors_updated: number;
      leads_pulled: number;
      leads_pushed: number;
      leads_updated: number;
      conflicts_resolved: number;
      errors: any[];
    };
    metadata: {
      timestamp: string;
      request_id: string | null;
      api_version: string;
      processing_time_ms: number | null;
    };
    error_code: number | null;
  }> => {
    const response = await api.post<{
      success: boolean;
      status: string;
      message: {
        title: string;
        description: string;
      };
      data: {
        franchisors_pulled: number;
        franchisors_pushed: number;
        franchisors_updated: number;
        leads_pulled: number;
        leads_pushed: number;
        leads_updated: number;
        conflicts_resolved: number;
        errors: any[];
      };
      metadata: {
        timestamp: string;
        request_id: string | null;
        api_version: string;
        processing_time_ms: number | null;
      };
      error_code: number | null;
    }>(`/zoho/sync`);
    return response.data;
  },
};
