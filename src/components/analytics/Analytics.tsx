import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';
// import { Calendar, Filter, Download, RefreshCw } from 'lucide-react';
import { DashboardAnalyticsResponse } from '../../services/dashboardService';
import { Loader2 } from 'lucide-react';

interface Props {
  chartData: DashboardAnalyticsResponse['data']['chart_data'];
  detailedData: DashboardAnalyticsResponse['data']['detailed_analytics'];
  loading: boolean;
}

const Analytics = ({ chartData, detailedData, loading }: Props) => {

  // Format data for charts
  const formattedChartData = chartData.map(item => ({
    date: item.date, 
    questionCount: item.question_count,
    escalationCount: item.escalation_count,
    escalationRate: item.escalation_rate,
  }));

  // Calculate totals
  // const totalSMS = analyticsData.reduce((sum, item) => sum + item.smsCount, 0);
  // const totalEscalations = analyticsData.reduce((sum, item) => sum + item.escalationCount, 0);
  // const avgSMSPerDay = Math.round(totalSMS / analyticsData.length);
  // const avgEscalationsPerDay = (totalEscalations / analyticsData.length).toFixed(1);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total SMS Sent</p>
              <p className="text-2xl font-bold text-gray-900 mt-2">{totalSMS}</p>
              <p className="text-sm text-gray-600 mt-1">Last 7 days</p>
            </div>
            <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg SMS Per Day</p>
              <p className="text-2xl font-bold text-gray-900 mt-2">{avgSMSPerDay}</p>
              <p className="text-sm text-gray-600 mt-1">Last 7 days</p>
            </div>
            <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="20" x2="12" y2="10"></line>
                <line x1="18" y1="20" x2="18" y2="4"></line>
                <line x1="6" y1="20" x2="6" y2="16"></line>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Escalations</p>
              <p className="text-2xl font-bold text-gray-900 mt-2">{totalEscalations}</p>
              <p className="text-sm text-gray-600 mt-1">Last 7 days</p>
            </div>
            <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Escalations Per Day</p>
              <p className="text-2xl font-bold text-gray-900 mt-2">{avgEscalationsPerDay}</p>
              <p className="text-sm text-gray-600 mt-1">Last 7 days</p>
            </div>
            <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </div>
          </div>
        </div>
      </div> */}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Question Count Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Question Count per Day{" "}
            </h2>
            {/* <p className="text-sm text-gray-600">Daily SMS messages sent</p> */}
          </div>
          <div className="h-80">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={formattedChartData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar
                    dataKey="questionCount"
                    name="Question Count"
                    fill="#3B82F6"
                  />
                </BarChart>
              </ResponsiveContainer>
            )}
          </div>
        </div>

        {/* Escalation Count Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Escalation Count Per Day
            </h2>
            {/* <p className="text-sm text-gray-600">Daily escalations to human agents</p> */}
          </div>
          <div className="h-80">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={formattedChartData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="escalationCount"
                    name="Escalation Count"
                    stroke="#F59E0B"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
        </div>
      </div>

      {/* Detailed Analytics Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-x-auto">
        <div className="p-4 sm:p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Detailed Analytics
          </h2>
        </div>
        <div className="overflow-x-auto">
          <div className="max-h-[500px] overflow-y-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">
                    Date
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">
                    Question Count
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">
                    Escalation Count
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">
                    Escalation Rate
                  </th>
                </tr>
              </thead>

              {loading ? (
                <tbody>
                  <tr>
                    <td colSpan={4} className="py-8 text-center text-gray-500">
                      <Loader2 className="w-5 h-5 animate-spin inline-block text-blue-600 mr-2" />
                      Loading analytics...
                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody className="divide-y divide-gray-200">
                  {detailedData.map((item, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="py-4 px-6 font-medium text-gray-900">
                        {item.date}
                      </td>
                      <td className="py-4 px-6 text-gray-700">
                        {item.question_count}
                      </td>
                      <td className="py-4 px-6 text-gray-700">
                        {item.escalation_count}
                      </td>
                      <td className="py-4 px-6 text-gray-700">
                        {item.escalation_rate.toFixed(1)}%
                      </td>
                    </tr>
                  ))}
                </tbody>
              )}
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;