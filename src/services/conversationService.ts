import api from "./api";

export interface ConversationMessage {
  id: string;
  created_at: string;
  franchisor_id: string;
  franchisor_name: string;
  is_deleted: boolean;
  lead_first_name: string;
  lead_id: string;
  lead_last_name: string;
  message: string;
  updated_at: string;
}

export interface ConversationData {
  items: ConversationMessage[];
  current_page: number;
  total_items: number;
  total_pages: number;
}

export interface ConversationResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: ConversationData;
  pagination: {
    current_page: number;
    total_pages: number;
    items_per_page: number;
    total_items: number;
  };
  metadata: {
    timestamp: string;
    request_id: string | null;
    api_version: string;
    processing_time_ms: number | null;
  };
  error_code: string | null;
}

export interface ConversationFilters {
  lead_id: string;
  page?: number;
  per_page?: number;
  order?: 'asc' | 'desc';
}

export interface DeleteMessageResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: any;
  metadata: {
    timestamp: string;
    request_id: string | null;
    api_version: string;
    processing_time_ms: number | null;
  };
  error_code: string | null;
}

export const conversationService = {
  /**
   * GET /api/conversation/conversation-messages/lead/{lead_id}
   * Get Messages by Lead ID
   */
  getMessagesByLeadId: async (filters: ConversationFilters): Promise<ConversationResponse> => {
    const params = new URLSearchParams();
    
    if (filters.page) params.append("page", filters.page.toString());
    if (filters.per_page) params.append("per_page", filters.per_page.toString());
    if (filters.order) params.append("order", filters.order);

    const url = `/conversation/conversation-messages/lead/${filters.lead_id}?${params.toString()}`;
    
    const response = await api.get<ConversationResponse>(url);
    return response.data;
  },

  /**
   * DELETE /api/conversation/conversation-messages/{message_id}
   * Delete Conversation Message
   */
  deleteMessage: async (messageId: string): Promise<DeleteMessageResponse> => {
    const response = await api.delete<DeleteMessageResponse>(
      `/conversation/conversation-messages/${messageId}`
    );
    return response.data;
  },

  /**
   * Get all conversation messages for all leads (for SMS listing page)
   */
  getAllConversations: async (params: {
    page?: number;
    per_page?: number;
    search?: string;
    franchisor_id?: string;
    order?: 'asc' | 'desc';
  } = {}): Promise<ConversationResponse> => {
    const query = new URLSearchParams();
    
    if (params.page) query.append("page", params.page.toString());
    if (params.per_page) query.append("per_page", params.per_page.toString());
    if (params.search) query.append("search", params.search);
    if (params.franchisor_id) query.append("franchisor_id", params.franchisor_id);
    if (params.order) query.append("order", params.order);

    const url = `/conversation/conversation-messages?${query.toString()}`;
    
    const response = await api.get<ConversationResponse>(url);
    return response.data;
  }
};
