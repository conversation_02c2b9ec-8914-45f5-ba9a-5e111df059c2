import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import ForgotPasswordLayout from "./ForgotPasswordLayout";
import toast from "react-hot-toast";
import * as Yup from "yup";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { Eye, EyeOff, Lock } from "lucide-react";
import { AxiosError } from "axios";
import { APIErrorResponse, authService } from "../../services/authService";

const schema = {
  reset: Yup.object().shape({
    newPassword: Yup.string()
      .required("New password is required")
      .min(6, "Password must be at least 6 characters"),
    confirmPassword: Yup.string()
      .required("Please confirm your password")
      .oneOf([Yup.ref("newPassword")], "Passwords must match"),
  }),
};

const ResetPassword = () => {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const user_id = localStorage.getItem("forgot_password_user_id") ?? "";
  const reset_code = localStorage.getItem("reset_code") ?? "";

  const handleReset = async (values: {
    newPassword: string;
    confirmPassword: string;
  }) => {
    setIsLoading(true);
    try {
      const response = await authService.resetForgotPassword({
        new_password: values.newPassword,
        confirm_password: values.confirmPassword,
        user_id,
        reset_code,
      });

      if (response?.success) {
        toast.success(
          response.message?.description || "Password reset successful!"
        );
        localStorage.removeItem("reset_code");
        localStorage.removeItem("forgot_password_user_id");
        navigate("/login");
      } else {
        toast.error("Password reset failed");
      }
    } catch (err: unknown) {
      const error = err as AxiosError<APIErrorResponse>;
      const errorMsg =
        error?.response?.data?.error?.message?.description ||
        error?.response?.data?.message?.description ||
        "Something went wrong";
      toast.error(errorMsg);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!user_id || !reset_code) {
      navigate("/forgot-password");
    }
  }, [user_id, reset_code, navigate]);

  if (!user_id || !reset_code) return null;

  return (
    <ForgotPasswordLayout
      title="Reset Password"
      description="Enter your new password"
    >
      <Formik
        initialValues={{ newPassword: "", confirmPassword: "" }}
        validationSchema={schema.reset}
        onSubmit={handleReset}
      >
        {({ isSubmitting }) => (
          <Form className="space-y-6">
            <div>
              <label
                htmlFor="newPassword"
                className="block text-sm font-medium mb-2"
              >
                New Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Field
                  id="newPassword"
                  name="newPassword"
                  type={showPassword ? "text" : "password"}
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg"
                  placeholder="Enter new password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
                >
                  {showPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
              </div>
              <ErrorMessage
                name="newPassword"
                component="div"
                className="text-red-500 text-sm mt-1"
              />
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium mb-2"
              >
                Confirm New Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Field
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg"
                  placeholder="Confirm new password"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
              </div>
              <ErrorMessage
                name="confirmPassword"
                component="div"
                className="text-red-500 text-sm mt-1"
              />
            </div>

            <button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 px-4 rounded-lg font-medium hover:from-yellow-700 hover:to-yellow-700 focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "Resetting..." : "Reset Password"}
            </button>
          </Form>
        )}
      </Formik>
    </ForgotPasswordLayout>
  );
};

export default ResetPassword;
