import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Loader2,
  Calendar,
  FileText,
} from "lucide-react";
import {
  categoryService,
  Category as CategoryType,
} from "../../services/categoryService";
import toast from "react-hot-toast";
import debounce from "lodash.debounce";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
} from "@tanstack/react-table";

const Category: React.FC = () => {
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [loading, setLoading] = useState(true);
  const [tableLoading, setTableLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<CategoryType | null>(null);
  const [viewingCategory, setViewingCategory] = useState<CategoryType | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [togglingStatus, setTogglingStatus] = useState<string | null>(null);
  const [savingCategory, setSavingCategory] = useState(false);
  const [deletingCategory, setDeletingCategory] = useState<string | null>(null);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
  const [totalItems, setTotalItems] = useState(0);

  // Non-debounced fetch for initial load and CRUD
  const fetchCategories = useCallback(
    async (
      params: { skip?: number; limit?: number; search?: string } = {
        skip: 0,
        limit: 10,
        search: "",
      }
    ) => {
      setLoading(true);
      try {
        const { items, pagination } = await categoryService.getCategories(
          params.skip ?? 0,
          params.limit ?? 10,
          params.search ?? ""
        );
        setCategories(items);
        setPagination((prev) => ({
          ...prev,
          pageIndex: pagination.currentPage - 1,
          pageSize: pagination.itemsPerPage,
        }));
        setTotalItems(pagination.totalItems); // crude estimate
      } catch (err) {
        toast.error("Failed to load categories.");
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Debounced fetch for search/filter
  const debouncedFetchCategories = useRef(
    debounce(
      async (params: { skip?: number; limit?: number; search?: string }) => {
        setTableLoading(true);
        try {
          const { items, pagination } = await categoryService.getCategories(
            params.skip ?? 0,
            params.limit ?? 10,
            params.search ?? ""
          );

          setCategories(items);
          setPagination((prev) => ({
            ...prev,
            pageIndex: pagination.currentPage - 1, // Convert to 0-based index
            pageSize: pagination.itemsPerPage,
          }));
          setTotalItems(pagination.totalItems);
        } catch (err) {
          toast.error("Failed to load categories.");
        } finally {
          setTableLoading(false);
        }
      },
      300
    )
  ).current;

  useEffect(() => {
    fetchCategories({
      skip: pagination.pageIndex * pagination.pageSize,
      limit: pagination.pageSize,
      search: "",
    });
  }, [pagination.pageIndex, pagination.pageSize]);

  useEffect(() => {
    if (searchTerm) {
      debouncedFetchCategories({
        skip: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        search: searchTerm,
      });
    } else {
      fetchCategories({
        skip: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        search: "",
      });
    }
    return () => {
      debouncedFetchCategories.cancel();
    };
  }, [searchTerm, pagination.pageIndex, pagination.pageSize]);

  const handleEdit = useCallback((category: CategoryType) => {
    setEditingCategory(category);
    setShowModal(true);
  }, []);

  const handleView = useCallback(async (category: CategoryType) => {
    try {
      const fullCategory = await categoryService.getCategoryById(category.id);
      setViewingCategory(fullCategory);
    } catch (err) {
      toast.error("Failed to load category details.");
    }
  }, []);

  const handleDelete = useCallback(async (id: string) => {
    if (confirm("Are you sure you want to delete this category?")) {
      try {
        setDeletingCategory(id);
        const success = await categoryService.deleteCategory(id);
        if (success) {
          setCategories((prevCategories) =>
            prevCategories.filter((c) => c.id !== id)
          );
          toast.success("Category deleted successfully!");
        } else {
          toast.error("Failed to delete category. Please try again.");
        }
      } catch (err) {
        console.error("Failed to delete category:", err);
        toast.error("Failed to delete category. Please try again.");
      } finally {
        setDeletingCategory(null);
      }
    }
  }, []);

  const handleSave = useCallback(
    async (categoryData: {
      name: string;
      description: string;
      is_active: boolean;
    }) => {
      try {
        setSavingCategory(true);
        if (editingCategory) {
          // Update existing category
          const updatedCategory = await categoryService.updateCategory(
            editingCategory.id,
            categoryData
          );
          setCategories((prevCategories) =>
            prevCategories.map((c) =>
              c.id === updatedCategory.id ? updatedCategory : c
            )
          );
          toast.success("Category updated successfully!");
        } else {
          // Add new category
          const newCategory = await categoryService.createCategory(
            categoryData
          );
          setCategories((prevCategories) => [...prevCategories, newCategory]);
          toast.success("Category created successfully!");
        }
        setShowModal(false);
      } catch (err) {
        console.error("Failed to save category:", err);
        toast.error("Failed to save category. Please try again.");
      } finally {
        setSavingCategory(false);
      }
    },
    [editingCategory]
  );

  const handleToggleStatus = useCallback(
    async (id: string, currentStatus: boolean) => {
      try {
        setTogglingStatus(id);
        const updatedCategory = await categoryService.toggleCategoryStatus(
          id,
          !currentStatus
        );
        setCategories((prevCategories) =>
          prevCategories.map((c) =>
            c.id === updatedCategory.id ? updatedCategory : c
          )
        );
        setViewingCategory(prev => 
          prev?.id === id ? updatedCategory : prev
        );
        toast.success(
          `Category ${
            !currentStatus ? "activated" : "deactivated"
          } successfully!`
        );
      } catch (err) {
        console.error("Failed to toggle category status:", err);
        toast.error("Failed to update category status. Please try again.");
      } finally {
        setTogglingStatus(null);
      }
    },
    []
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
    },
    []
  );

  const handleAddCategory = useCallback(() => {
    setEditingCategory(null);
    setShowModal(true);
  }, []);

  // TanStack Table columns
  const columns = React.useMemo<ColumnDef<CategoryType, unknown>[]>(
    () => [
      {
        accessorKey: "name",
        header: "Category Name",
        cell: (info) => (
          <div>
            <div className="font-medium text-gray-900">
              {info.row.original.name}
            </div>
          </div>
        ),
      },
      {
        accessorKey: "description",
        header: "Description",
        cell: (info) => (
          <span className="text-gray-700">{info.row.original.description}</span>
        ),
      },
      {
        accessorKey: "is_active",
        header: "Active",
        cell: (info) => {
          const category = info.row.original;
          return (
            <div className="flex items-center space-x-3">
              <button
                onClick={() =>
                  handleToggleStatus(category.id, category.is_active)
                }
                disabled={togglingStatus === category.id}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  category.is_active ? "bg-blue-600" : "bg-gray-200"
                } ${
                  togglingStatus === category.id
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    category.is_active ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          );
        },
      },
      {
        accessorKey: "created_at",
        header: "Created Date",
        cell: (info) => (
          <span className="text-gray-700">
            {new Date(info.row.original.created_at).toLocaleDateString()}
          </span>
        ),
      },
      {
        id: "actions",
        header: "Actions",
        cell: (info) => {
          const category = info.row.original;
          return (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleEdit(category)}
                className="text-blue-600 hover:text-blue-800 p-1"
                title="Edit"
              >
                <Edit className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleView(category)}
                className="text-green-600 hover:text-green-800 p-1"
                title="View Details"
              >
                <Eye className="w-4 h-4" />
              </button>
              {/* <button
                onClick={() => handleDelete(category.id)}
                disabled={deletingCategory === category.id}
                className={`text-red-600 hover:text-red-800 p-1 ${
                  deletingCategory === category.id ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                title="Delete"
              >
                {deletingCategory === category.id ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
              </button> */}
            </div>
          );
        },
      },
    ],
    [togglingStatus, deletingCategory, handleEdit]
  );

  const table = useReactTable({
    data: categories,
    columns,
    pageCount: totalItems > 0 ? Math.ceil(totalItems / pagination.pageSize) : 1,
    state: {
      pagination: {
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
      },
    },
    onPaginationChange: (updater) => {
    const newPagination = typeof updater === 'function' 
      ? updater({
          pageIndex: pagination.pageIndex,
          pageSize: pagination.pageSize,
        })
      : updater;
    
    setPagination(newPagination);
    fetchCategories({
      skip: newPagination.pageIndex * newPagination.pageSize,
      limit: newPagination.pageSize,
    });
  },
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualFiltering: true,
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading categories...</div>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Categories Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your business categories and classifications
          </p>
        </div>
        <button
          onClick={handleAddCategory}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Category</span>
        </button>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          {/* <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Filter className="w-4 h-4" />
            <span>Filter</span>
          </button> */}
        </div>
      </div>

      {/* Categories List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 relative">
        {tableLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading categories...</span>
          </div>
        ) : categories.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg">No categories found</div>
            <div className="text-gray-400 text-sm mt-1">
              Try adjusting your search or filters
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                {table.getHeaderGroups().map((headerGroup) => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <th
                        key={header.id}
                        className="text-left py-4 px-6 font-medium text-gray-900"
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody className="divide-y divide-gray-200">
                {table.getRowModel().rows.map((row) => (
                  <tr key={row.id} className="hover:bg-gray-50">
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className="py-4 px-6">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
            {/* Pagination Controls */}
            <div className="border-t border-gray-200 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing {pagination.pageIndex * pagination.pageSize + 1} to{" "}
                  {Math.min(
                    (pagination.pageIndex + 1) * pagination.pageSize,
                    totalItems
                  )}{" "}
                  of {totalItems} results
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => table.previousPage()}
                    disabled={!table.getCanPreviousPage()}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <div className="flex items-center space-x-1">
                    {Array.from(
                      { length: Math.min(5, table.getPageCount()) },
                      (_, i) => {
                        const page = i + 1;
                        return (
                          <button
                            key={page}
                            onClick={() => table.setPageIndex(page - 1)}
                            className={`px-3 py-2 text-sm font-medium rounded-lg ${
                              pagination.pageIndex === page - 1
                                ? "bg-blue-600 text-white"
                                : "text-gray-500 bg-white border border-gray-300 hover:bg-gray-50"
                            }`}
                          >
                            {page}
                          </button>
                        );
                      }
                    )}
                  </div>
                  <button
                    onClick={() => table.nextPage()}
                    disabled={!table.getCanNextPage()}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Add/Edit Modal */}
      {showModal && (
        <CategoryModal
          category={editingCategory}
          onClose={() => setShowModal(false)}
          onSave={handleSave}
          saving={savingCategory}
        />
      )}

      {/* View Details Modal */}
      {viewingCategory && (
        <CategoryDetailsModal
          category={viewingCategory}
          onClose={() => setViewingCategory(null)}
          onToggleStatus={handleToggleStatus}
          togglingStatus={togglingStatus}
        />
      )}
    </div>
  );
};

const CategoryModal: React.FC<{
  category: CategoryType | null;
  onClose: () => void;
  onSave: (categoryData: { name: string; description: string; is_active: boolean }) => void;
  saving: boolean;
}> = ({ category, onClose, onSave, saving }) => {
  const [formData, setFormData] = useState({
    name: category?.name || '',
    description: category?.description || '',
    is_active: category?.is_active ?? true
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {category ? 'Edit Category' : 'Add New Category'}
          </h2>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              disabled={saving}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              required
              disabled={saving}
            />
          </div>
          
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                disabled={saving}
              />
              <span className="ml-2 text-sm text-gray-700">Active Category</span>
            </label>
          </div>
          
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              disabled={saving}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>{category ? 'Updating...' : 'Creating...'}</span>
                </>
              ) : (
                <span>{category ? 'Update' : 'Create'}</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// New component for viewing category details
const CategoryDetailsModal: React.FC<{
  category: CategoryType;
  onClose: () => void;
  onToggleStatus: (id: string, currentStatus: boolean) => void;
  togglingStatus: string | null;
}> = ({ category, onClose, onToggleStatus, togglingStatus }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Category Details</h2>
        </div>
        
        <div className="p-6">
          {/* Category Header */}
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{category.name}</h1>
              <p className="text-sm text-gray-500 mt-1">ID: {category.id}</p>
            </div>
            <div className="mt-4 md:mt-0">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => onToggleStatus(category.id, category.is_active)}
                  disabled={togglingStatus === category.id}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    category.is_active ? 'bg-blue-600' : 'bg-gray-200'
                  } ${togglingStatus === category.id ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      category.is_active ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
          
          {/* Category Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <FileText className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">Description</h3>
              </div>
              <p className="text-gray-700 ml-8">{category.description}</p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <Calendar className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">Created Date</h3>
              </div>
              <p className="text-gray-700 ml-8">{new Date(category.created_at).toLocaleDateString()}</p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <Calendar className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">Last Updated</h3>
              </div>
              <p className="text-gray-700 ml-8">{new Date(category.updated_at).toLocaleDateString()}</p>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Close
            </button>
          
          </div>
        </div>
      </div>
    </div>
  );
};

export default Category; 