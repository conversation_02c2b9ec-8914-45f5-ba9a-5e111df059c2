import React, { useState } from 'react';
import { Plus, Search, Filter, Calendar, Link, Copy, Send, Check, X } from 'lucide-react';
import { Lead, Franchisor } from '../../types';
import DeleteConfirmModal from '../common/DeleteConfirmModal';
import Select from '../common/Select';

interface BookingLink {
  id: string;
  name: string;
  url: string;
  franchiseId: string;
  createdAt: Date;
  type: 'discovery' | 'follow-up' | 'qualification' | 'custom';
  duration: 15 | 30 | 45 | 60;
}

const BookingLinks: React.FC = () => {
  // Sample data for booking links
  const [bookingLinks, setBookingLinks] = useState<BookingLink[]>([
    {
      id: '1',
      name: 'Pizza Express Discovery Call',
      url: 'https://calendly.com/growthhive/pizza-express-discovery',
      franchiseId: '1',
      createdAt: new Date('2024-04-15'),
      type: 'discovery',
      duration: 30
    },
    {
      id: '2',
      name: 'Fitness First Qualification Call',
      url: 'https://calendly.com/growthhive/fitness-first-qualification',
      franchiseId: '2',
      createdAt: new Date('2024-04-20'),
      type: 'qualification',
      duration: 45
    },
    {
      id: '3',
      name: 'General Follow-up Meeting',
      url: 'https://calendly.com/growthhive/follow-up',
      franchiseId: '',
      createdAt: new Date('2024-04-10'),
      type: 'follow-up',
      duration: 15
    }
  ]);

  // Sample data for franchisors
  const [franchisors, setFranchisors] = useState<Franchisor[]>([
    {
      id: '1',
      name: 'Pizza Express',
      category: 'Food & Beverage',
      region: 'North America',
      budget: 150000,
      subCategory: 'Quick Service',
      createdAt: new Date('2024-01-15')
    },
    {
      id: '2',
      name: 'Fitness First',
      category: 'Health & Fitness',
      region: 'Europe',
      budget: 250000,
      subCategory: 'Gym',
      createdAt: new Date('2024-01-20')
    }
  ]);

  // Sample data for leads
  const [leads, setLeads] = useState<Lead[]>([
    {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '******-0123',
      source: 'manual',
      status: 'new',
      franchiseInterest: 'Pizza Express',
      createdAt: new Date('2024-01-15'),
      communications: []
    },
    {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '******-0124',
      source: 'zoho',
      status: 'qualified',
      franchiseInterest: 'Fitness First',
      createdAt: new Date('2024-01-20'),
      communications: []
    }
  ]);

  const [showModal, setShowModal] = useState(false);
  const [showSendModal, setShowSendModal] = useState(false);
  const [editingLink, setEditingLink] = useState<BookingLink | null>(null);
  const [selectedLink, setSelectedLink] = useState<BookingLink | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterFranchise, setFilterFranchise] = useState('');
  const [filterType, setFilterType] = useState('');
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
  const [deleteModal, setDeleteModal] = useState<{
    open: boolean;
    id: string | null;
  }>({ open: false, id: null });

  // Get franchise name by ID
  const getFranchiseName = (franchiseId: string) => {
    if (!franchiseId) return 'Generic (All Brands)';
    const franchise = franchisors.find(f => f.id === franchiseId);
    return franchise ? franchise.name : 'Unknown Brand';
  };

  // Filter booking links based on search term and filters
  const filteredLinks = bookingLinks.filter(link => {
    const matchesSearch = link.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         link.url.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFranchise = filterFranchise === '' || link.franchiseId === filterFranchise;
    const matchesType = filterType === '' || link.type === filterType;
    return matchesSearch && matchesFranchise && matchesType;
  });

  // Handle link deletion
  const handleDeleteRequest = (id: string) => {
    setDeleteModal({ open: true, id });
  };

  const handleDeleteConfirm = () => {
    if (!deleteModal.id) return;
    setBookingLinks((prev) =>
      prev.filter((link) => link.id !== deleteModal.id)
    );
    setDeleteModal({ open: false, id: null });
  };

  // Handle link edit
  const handleEdit = (link: BookingLink) => {
    setEditingLink(link);
    setShowModal(true);
  };

  // Handle copy to clipboard
  const handleCopy = (url: string, id: string) => {
    navigator.clipboard.writeText(url);
    setCopiedId(id);
    setTimeout(() => setCopiedId(null), 2000);
  };

  // Handle send booking link
  const handleSend = (link: BookingLink) => {
    setSelectedLink(link);
    setSelectedLeads([]);
    setShowSendModal(true);
  };

  // Toggle lead selection
  const toggleLeadSelection = (leadId: string) => {
    if (selectedLeads.includes(leadId)) {
      setSelectedLeads(selectedLeads.filter(id => id !== leadId));
    } else {
      setSelectedLeads([...selectedLeads, leadId]);
    }
  };

  // Send booking invites
  const sendInvites = () => {
    // In a real app, this would send emails to the selected leads
    // and franchise owner with the booking link
    
    // For this demo, we'll just show a success message
    alert(`Booking invites sent to ${selectedLeads.length} leads for ${selectedLink?.name}`);
    setShowSendModal(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Booking Links</h1>
          <p className="text-gray-600 mt-1">Create and manage Calendly booking links for your franchises</p>
        </div>
        <button
          onClick={() => {
            setEditingLink(null);
            setShowModal(true);
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Booking Link</span>
        </button>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search booking links..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="sm:w-64">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <Filter className="h-5 w-5 text-gray-400" />
              </div>
              <Select
                value={filterFranchise}
                onChange={setFilterFranchise}
                options={[
                  { value: "", label: "All Brands" },
                  { value: "", label: "Generic (No Brand)" },
                  ...franchisors.map(franchise => ({
                    value: franchise.id,
                    label: franchise.name
                  }))
                ]}
                className="pl-10"
              />
            </div>
          </div>
          <div className="sm:w-64">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Meeting Types</option>
                <option value="discovery">Discovery Call</option>
                <option value="follow-up">Follow-up Meeting</option>
                <option value="qualification">Qualification Call</option>
                <option value="custom">Custom Meeting</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Booking Links List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="text-left py-4 px-6 font-medium text-gray-900">Link Name</th>
                <th className="text-left py-4 px-6 font-medium text-gray-900">Brand</th>
                <th className="text-left py-4 px-6 font-medium text-gray-900">Type</th>
                <th className="text-left py-4 px-6 font-medium text-gray-900">Duration</th>
                <th className="text-left py-4 px-6 font-medium text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredLinks.map((link) => (
                <tr key={link.id} className="hover:bg-gray-50">
                  <td className="py-4 px-6">
                    <div className="font-medium text-gray-900">{link.name}</div>
                    <div className="text-sm text-gray-500 truncate max-w-xs">
                      {link.url}
                    </div>
                  </td>
                  <td className="py-4 px-6 text-gray-700">
                    {getFranchiseName(link.franchiseId)}
                  </td>
                  <td className="py-4 px-6 text-gray-700 capitalize">
                    {link.type.replace('-', ' ')}
                  </td>
                  <td className="py-4 px-6 text-gray-700">
                    {link.duration} minutes
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleCopy(link.url, link.id)}
                        className={`${copiedId === link.id ? 'text-green-600' : 'text-blue-600 hover:text-blue-800'} p-1`}
                        title="Copy Link"
                      >
                        {copiedId === link.id ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                      </button>
                      <button
                        onClick={() => handleSend(link)}
                        className="text-purple-600 hover:text-purple-800 p-1"
                        title="Send to Leads"
                      >
                        <Send className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEdit(link)}
                        className="text-green-600 hover:text-green-800 p-1"
                        title="Edit Link"
                      >
                        <Link className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteRequest(link.id)}
                        className="text-red-600 hover:text-red-800 p-1"
                        title="Delete Link"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add/Edit Booking Link Modal */}
      {showModal && (
        <BookingLinkModal
          link={editingLink}
          franchisors={franchisors}
          onClose={() => setShowModal(false)}
          onSave={(link) => {
            if (editingLink) {
              // Update existing link
              setBookingLinks(bookingLinks.map(l => l.id === link.id ? link : l));
            } else {
              // Add new link
              setBookingLinks([...bookingLinks, { ...link, id: Date.now().toString(), createdAt: new Date() }]);
            }
            setShowModal(false);
          }}
        />
      )}

      {/* Send Booking Link Modal */}
      {showSendModal && selectedLink && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Send Booking Link</h2>
              <p className="text-sm text-gray-600 mt-1">
                Select leads to send the booking link: <span className="font-medium">{selectedLink.name}</span>
              </p>
            </div>
            
            <div className="p-6">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Booking Link Details
                </label>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-700"><span className="font-medium">Name:</span> {selectedLink.name}</p>
                  <p className="text-sm text-gray-700"><span className="font-medium">Brand:</span> {getFranchiseName(selectedLink.franchiseId)}</p>
                  <p className="text-sm text-gray-700"><span className="font-medium">Type:</span> {selectedLink.type.replace('-', ' ')}</p>
                  <p className="text-sm text-gray-700"><span className="font-medium">Duration:</span> {selectedLink.duration} minutes</p>
                  <p className="text-sm text-gray-700 truncate"><span className="font-medium">URL:</span> {selectedLink.url}</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Leads
                </label>
                <div className="border border-gray-300 rounded-lg max-h-64 overflow-y-auto">
                  {leads.length > 0 ? (
                    <ul className="divide-y divide-gray-200">
                      {leads.map(lead => (
                        <li key={lead.id} className="p-3 hover:bg-gray-50">
                          <label className="flex items-center space-x-3 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={selectedLeads.includes(lead.id)}
                              onChange={() => toggleLeadSelection(lead.id)}
                              className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                            />
                            <div>
                              <p className="font-medium text-gray-900">{lead.name}</p>
                              <p className="text-sm text-gray-500">{lead.email} • {lead.franchiseInterest}</p>
                            </div>
                          </label>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="p-4 text-center text-gray-500">No leads available</p>
                  )}
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  {selectedLeads.length} lead(s) selected
                </p>
              </div>
              
              <div className="flex space-x-3 pt-6">
                <button
                  type="button"
                  onClick={() => setShowSendModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={sendInvites}
                  disabled={selectedLeads.length === 0}
                  className={`flex-1 px-4 py-2 rounded-lg ${
                    selectedLeads.length === 0 
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  Send Booking Invites
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirm Modal */}
      <DeleteConfirmModal
        open={deleteModal.open}
        onCancel={() => setDeleteModal({ open: false, id: null })}
        onConfirm={handleDeleteConfirm}
        title="Delete Booking Link"
        message="Are you sure you want to delete this booking link? This action cannot be undone."
      />
    </div>
  );
};

// Booking Link Modal Component
const BookingLinkModal: React.FC<{
  link: BookingLink | null;
  franchisors: Franchisor[];
  onClose: () => void;
  onSave: (link: BookingLink) => void;
}> = ({ link, franchisors, onClose, onSave }) => {
  const [formData, setFormData] = useState<Omit<BookingLink, 'id' | 'createdAt'>>({
    name: link?.name || '',
    url: link?.url || '',
    franchiseId: link?.franchiseId || '',
    type: link?.type || 'discovery',
    duration: link?.duration || 30
  });

  const handleSave = () => {
    const newLink: BookingLink = {
      ...formData,
      id: link?.id || Date.now().toString(),
      createdAt: link?.createdAt || new Date()
    };
    onSave(newLink);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Add/Edit Booking Link</h2>
        </div>
        <div className="p-6">
          <form>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Link Name</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">URL</label>
              <input
                type="url"
                value={formData.url}
                onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
              <select
                value={formData.franchiseId}
                onChange={(e) => setFormData({ ...formData, franchiseId: e.target.value })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Brands</option>
                <option value="">Generic (No Brand)</option>
                {franchisors.map(franchise => (
                  <option key={franchise.id} value={franchise.id}>
                    {franchise.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
              <select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value as BookingLink['type'] })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="discovery">Discovery Call</option>
                <option value="follow-up">Follow-up Meeting</option>
                <option value="qualification">Qualification Call</option>
                <option value="custom">Custom Meeting</option>
              </select>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Duration (minutes)</label>
              <select
                value={formData.duration}
                onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) as BookingLink['duration'] })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="15">15</option>
                <option value="30">30</option>
                <option value="45">45</option>
                <option value="60">60</option>
              </select>
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSave}
                className="flex-1 px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BookingLinks;
