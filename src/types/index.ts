export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
}

export interface Franchisor {
  id: string;
  name: string;
  category: string;
  region: string;
  budget: number;
  subCategory: string;
  createdAt: Date;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
}

export interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  postalCode?: string;
  source: "csv" | "zoho" | "manual";
  status:
    | "new"
    | "qualified"
    | "contacted"
    | "converted"
    | "call-back"
    | "not-interested"
    | "no-answer"
    | "answering-machine";
  franchiseInterest: string;
  createdAt: Date;
  communications: Communication[];
  notes?: string;
  lookingDuration?: "just-recently" | "all-my-life" | "6-months" | "12-months";
  availableFunds?: number;
}

export interface Communication {
  id: string;
  type: "sms" | "email" | "call";
  content: string;
  timestamp: Date;
  direction: "inbound" | "outbound";
}

export interface Document {
  id: string;
  name: string;
  type: string;
  url: string;
  franchiseId: string;
  uploadedAt: Date;
}

export interface SalesScript {
  id: string;
  title: string;
  content: string;
  franchiseId: string;
  createdAt: Date;
}

export interface Question {
  id: string;
  text: string;
  type: string,
  isActive: boolean;
  franchisorId?: string;
  expected_answer?: string[];
  expected_answer_type: string,
  answer_options: string[],
  score_weight: number,
  qualification_weight: number,
  passing_criteria: string | string[],         
  validation_rules: string,
  requires_follow_up: boolean,
  follow_up_logic?: string,
  is_required: boolean,
  category: string,
  [key: string]: any
}

export interface Analytics {
  smsCount: number;
  escalationCount: number;
  date: Date;
}
