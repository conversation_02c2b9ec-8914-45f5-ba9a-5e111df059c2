import api from "./api";

export interface Category {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
}

export interface CategoryResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: {
    items: Category[];
    total_count: number;
    pagination: {
      current_page: number;
      total_pages: number;
      items_per_page: number;
      total_items: number;
    };
  };
  metadata: {
    timestamp: string;
    request_id: string | null;
    api_version: string;
    processing_time_ms: number | null;
  };
  error_code: number | null;
}

export interface SingleCategoryResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: {
    details: [string, any][]; // Single category still returns [key, value] pairs
    pagination: any;
  };
  error_code: number;
}

export interface CreateCategoryRequest {
  name: string;
  description: string;
  is_active: boolean;
  is_deleted?: boolean;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  is_active?: boolean;
  is_deleted?: boolean;
}

// Helper function to convert API response details array to Category object
const convertDetailsToCategory = (details: [string, any][]): Category => {
  const category: any = {};
  details.forEach(([key, value]) => {
    category[key] = value;
  });
  return category as Category;
};

export const categoryService = {
  getCategories: async (
    skip: number = 0,
    limit: number = 100,
    search: string | null,
    sortBy?: string | null,
    sortOrder?: string | null
  ): Promise<{
    items: Category[];
    total_count: number;
    pagination: {
      currentPage: number;
      totalPages: number;
      itemsPerPage: number;
      totalItems: number;
    };
  }> => {
    const url = `/industries?skip=${skip}&limit=${limit}&search=${search}${
    sortBy ? `&sort_by=${sortBy}` : ''
  }${sortOrder ? `&sort_order=${sortOrder}` : ''}`;
    const response = await api.get<CategoryResponse>(url);
    return {
      items: response.data.data.items,
      total_count: response.data.data.total_count,
      pagination: {
        currentPage: response.data.data.pagination.current_page,
        totalPages: response.data.data.pagination.total_pages,
        itemsPerPage: response.data.data.pagination.items_per_page,
        totalItems: response.data.data.pagination.total_items,
      }
    };
    // return response.data.data.items;
  },

  getCategoryById: async (id: string): Promise<Category> => {
    const response = await api.get<SingleCategoryResponse>(`/industries/${id}`);
    return convertDetailsToCategory(response.data.data.details);
  },

  createCategory: async (categoryData: CreateCategoryRequest): Promise<Category> => {
    const response = await api.post<SingleCategoryResponse>("/industries", categoryData);
    return convertDetailsToCategory(response.data.data.details);
  },

  updateCategory: async (id: string, categoryData: UpdateCategoryRequest): Promise<Category> => {
    const response = await api.put<SingleCategoryResponse>(`/industries/${id}`, categoryData);
    return convertDetailsToCategory(response.data.data.details);
  },

  deleteCategory: async (id: string): Promise<boolean> => {
    const response = await api.delete<{ success: boolean }>(`/industries/${id}`);
    return response.data.success;
  },

  toggleCategoryStatus: async (id: string, is_active: boolean): Promise<Category> => {
    const response = await api.put<SingleCategoryResponse>(`/industries/${id}`, { is_active });
    return convertDetailsToCategory(response.data.data.details);
  },
}; 