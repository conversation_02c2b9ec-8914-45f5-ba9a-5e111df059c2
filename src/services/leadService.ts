import api from "./api";

export interface Lead {
  id: string;
  zoho_lead_id?: string | null;
  first_name: string;
  last_name: string | null;
  phone: string;
  mobile: string;
  email: string;
  location: string;
  postal_code: string | null;
  lead_source_id: string | null;
  lead_source_name: string | null;
  lead_status_id: string;
  lead_status_name: string;
  lead_status_colour: string;
  brand_preference: string;
  budget_preference: string;
  franchise_interested_in: string | null;
  looking_for_business_opportunity_since: string | null;
  skills: string | null;
  looking_to_be_owner_operator: string | null;
  when_looking_to_start: string | null;
  ethnic_background: string | null;
  funds_to_invest: string | null;
  eoi_nda_link: string | null;
  work_background: string | null;
  motivation_to_enquire: string | null;
  funds_available: string | null;
  motivation: string | null;
  have_run_business_before: string | null;
  have_mortgage: string | null;
  high_net_worth: string | null;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
}

export interface LeadSource {
  id: string;
  name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface LeadStatus {
  id: string;
  name: string;
  colour: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface LeadListResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: {
    items: Lead[];
    total_count: number;
    pagination: {
      current_page: number;
      total_pages: number;
      items_per_page: number;
      total_items: number;
    };
  };
}

export interface LeadDetailResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: Lead;
}

export interface CreateLeadRequest {
  first_name: string;
  last_name?: string;
  phone: string;
  mobile: string;
  email: string;
  location: string;
  postal_code?: string;
  lead_source_id: string;
  lead_status_id: string;
  brand_preference: string;
  budget_preference: string;
}

export interface UpdateLeadRequest {
  first_name?: string;
  last_name?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  location?: string;
  postal_code?: string;
  lead_source_id?: string;
  lead_status_id?: string;
  brand_preference?: string;
  budget_preference?: string;
  franchise_interested_in?: string;
  looking_for_business_opportunity_since?: string;
  skills?: string;
  looking_to_be_owner_operator?: string;
  when_looking_to_start?: string;
  ethnic_background?: string;
  funds_to_invest?: string;
  eoi_nda_link?: string;
  work_background?: string;
  motivation_to_enquire?: string;
  funds_available?: string;
  motivation?: string;
  have_run_business_before?: string;
  have_mortgage?: string;
  high_net_worth?: string;
  is_active?: boolean;
}

export interface SyncMessage {
  title: string;
  description: string;
}

export interface SyncData {
  franchisors_pulled: number;
  franchisors_pushed: number;
  franchisors_updated: number;
  leads_pulled: number;
  leads_pushed: number;
  leads_updated: number;
  conflicts_resolved: number;
  errors: any[];
}

export interface SyncMetadata {
  timestamp: string;
  request_id: string | null;
  api_version: string;
  processing_time_ms: number | null;
}

export interface ZohoSyncResponse {
  success: boolean;
  status: string;
  message: SyncMessage;
  data: SyncData;
  metadata: SyncMetadata;
  error_code: number | null;
}


export const leadService = {
  getLeads: async (params: {
    skip?: number;
    limit?: number;
    lead_status_id?: string;
    search?: string;
    lead_source_id?: string;
    created_from?: string;
    created_to?: string;
    sort?: string;
  } = {}): Promise<LeadListResponse> => {
    const query = new URLSearchParams();
    if (params.skip !== undefined) query.append("skip", String(params.skip));
    if (params.limit !== undefined) query.append("limit", String(params.limit));
    if (params.lead_status_id) query.append("lead_status_id", params.lead_status_id);
    if (params.search) query.append("search", params.search);
    if (params.lead_source_id) query.append("lead_source_id", params.lead_source_id);
    if (params.created_from) query.append("created_from", params.created_from);
    if (params.created_to) query.append("created_to", params.created_to);
    if (params.sort) query.append("sort", params.sort);
    const response = await api.get<LeadListResponse>(`/leads/?${query.toString()}`);
    return response.data;
  },

  getLeadSources: async (): Promise<{ success: boolean; data: LeadSource[] }> => {
    try {
      console.log('🔍 Fetching lead sources from /dropdowns/lead-sources');
      const response = await api.get('/dropdowns/lead-sources');
      console.log('🔍 Raw lead sources API response:', response.data);

      // Check if response has data.items array (primary format)
      if (response.data && response.data.data && response.data.data.items && Array.isArray(response.data.data.items)) {
        console.log('✅ Lead sources found in data.items:', response.data.data.items.length);
        return {
          success: true,
          data: response.data.data.items
        };
      }

      // Check if response has items array (fallback)
      if (response.data && response.data.items && Array.isArray(response.data.items)) {
        console.log('✅ Lead sources found in items:', response.data.items.length);
        return {
          success: true,
          data: response.data.items
        };
      }

      // Check if response is directly an array (fallback)
      if (Array.isArray(response.data)) {
        console.log('✅ Lead sources found as direct array:', response.data.length);
        return {
          success: true,
          data: response.data
        };
      }

      console.error('❌ Unexpected lead sources response format:', response.data);
      return { success: false, data: [] };
    } catch (error: any) {
      console.error('❌ Failed to fetch lead sources:', error.response?.status, error.response?.data);
      return { success: false, data: [] };
    }
  },

  getLeadStatuses: async (): Promise<{ success: boolean; data: LeadStatus[] }> => {
    try {
      console.log('🔍 Fetching lead statuses from /dropdowns/lead-statuses');
      const response = await api.get('/dropdowns/lead-statuses');
      console.log('🔍 Raw lead statuses API response:', response.data);

      // Check if response has data.items array (primary format)
      if (response.data && response.data.data && response.data.data.items && Array.isArray(response.data.data.items)) {
        console.log('✅ Lead statuses found in data.items:', response.data.data.items.length);
        return {
          success: true,
          data: response.data.data.items
        };
      }

      // Check if response has items array (fallback)
      if (response.data && response.data.items && Array.isArray(response.data.items)) {
        console.log('✅ Lead statuses found in items:', response.data.items.length);
        return {
          success: true,
          data: response.data.items
        };
      }

      // Check if response is directly an array (fallback)
      if (Array.isArray(response.data)) {
        console.log('✅ Lead statuses found as direct array:', response.data.length);
        return {
          success: true,
          data: response.data
        };
      }

      console.error('❌ Unexpected lead statuses response format:', response.data);
      return { success: false, data: [] };
    } catch (error: any) {
      console.error('❌ Failed to fetch lead statuses:', error.response?.status, error.response?.data);
      return { success: false, data: [] };
    }
  },

  getLeadById: async (id: string): Promise<LeadDetailResponse> => {
    const response = await api.get<LeadDetailResponse>(`/leads/${id}`);
    return response.data;
  },

  createLead: async (leadData: CreateLeadRequest): Promise<LeadDetailResponse> => {
    const response = await api.post<LeadDetailResponse>("/leads/", leadData);
    return response.data;
  },

  updateLead: async (id: string, leadData: UpdateLeadRequest): Promise<LeadDetailResponse> => {
    const response = await api.put<LeadDetailResponse>(`/leads/${id}`, leadData);
    return response.data;
  },

  deleteLead: async (id: string): Promise<{ success: boolean; status: string; message: any; data: any }> => {
    const response = await api.delete<{ success: boolean; status: string; message: any; data: any }>(`/leads/${id}`);
    return response.data;
  },

  bulkUploadLeads: async (file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);
    const response = await api.post('/leads/bulk-upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  toggleLeadStatus: async (id: string, is_active: boolean): Promise<Lead> => {
    const response = await api.put<LeadDetailResponse>(`/leads/${id}`, { is_active });
    return response.data.data;
  },

  /**
   * Toggle leads active status
   * Route: PATCH /leads/:lead_id/activate or /leads/:lead_id/deactivate
   */
  toggleStatus: async (lead_id: string, setActive: boolean): Promise<boolean> => {
    const endpoint = setActive ? 'activate' : 'deactivate';
    const response = await api.patch<{
      success: boolean;
      status: string;
      message: {
        title: string;
        description: string;
      }
    }>(`leads/${lead_id}/${endpoint}`);
    
    return response.data.success;
  },

/**
   * Sync leads/franchisors with Zoho CRM
   * Route: POST /zoho/sync
   */
  syncWithZoho: async (): Promise<ZohoSyncResponse> => {
    const response = await api.post<ZohoSyncResponse>(`/zoho/sync`);
    return response.data;
  },

}; 