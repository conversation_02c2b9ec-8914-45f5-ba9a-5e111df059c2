include:
  remote: 'https://${CI_SERVER_HOST}/public-resources/gitlab-ci/-/raw/master/templates/build.yaml'

stages:
  - build
  - deploy

variables:
  NGINX_PATH: "/home/<USER>/docker-stack/nginx_conf/react"
  PROJECT: "growthhive-cms"

build: 
  stage: build
  image:
    name: node:20-alpine
  extends: .build_static   
  artifacts:
    paths: 
     - dist/*

deploy_dev:
  stage: deploy
  extends: .deploy_static
  variables:
    BUILD_PATH: dist
  only:
    - development
# build: 
#   stage: build
#   image:
#     name: node:20-alpine
#   extends: .build_static
#   artifacts:
#     paths: 
#      - dist/*
#   only:
#     - development   

# deploy_dev:
#   stage: deploy
#   extends: .deploy_static
#   variables:
#     BUILD_PATH: dist
#   only:
#     - development

