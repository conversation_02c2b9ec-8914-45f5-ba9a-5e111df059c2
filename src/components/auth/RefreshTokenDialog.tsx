import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { AlertTriangle, RefreshCw, LogOut } from 'lucide-react';

const RefreshTokenDialog: React.FC = () => {
  const { showRefreshDialog, acceptRefresh, rejectRefresh } = useAuth();

  if (!showRefreshDialog) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
        <div className="flex items-center mb-4">
          <AlertTriangle className="w-6 h-6 text-yellow-500 mr-3" />
          <h2 className="text-lg font-semibold text-gray-900">Session Expiring</h2>
        </div>
        
        <p className="text-gray-600 mb-6">
          Your session is about to expire. Would you like to extend your session or log out?
        </p>
        
        <div className="flex space-x-3">
          <button
            onClick={acceptRefresh}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Extend Session
          </button>
          
          <button
            onClick={rejectRefresh}
            className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Log Out
          </button>
        </div>
      </div>
    </div>
  );
};

export default RefreshTokenDialog;
