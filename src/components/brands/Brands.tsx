import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Upload,
  Eye,
  Loader2,
  RefreshCw,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Filter,
  Download,
} from "lucide-react";
import Select from '../common/Select';
import LoadingOverlay from '../common/LoadingOverlay';
import toast from "react-hot-toast";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
  SortingState,
} from "@tanstack/react-table";
import {
  franchisorsService,
  Franchisor,
  FranchisorsFilters,
  CategoryOption,
} from "../../services/franchisorsService";
import DeleteConfirmModal from "../common/DeleteConfirmModal";
import Pagination from "../common/Pagination";

const Brands = () => {
  const navigate = useNavigate();

  // State management
  const [franchisors, setFranchisors] = useState<Franchisor[]>([]);
  const [loading, setLoading] = useState({
    page: false,
    initial: true,
    sync: false,
    import: false,
  });

  // Sync overlay states
  const [showSyncOverlay, setShowSyncOverlay] = useState(false);
  const [syncSuccess, setSyncSuccess] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [industryFilter, setIndustryFilter] = useState("");
  const [regionFilter, setRegionFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState<boolean | null>(null);
  const [categories, setCategories] = useState<CategoryOption[]>([]);
  const [sorting, setSorting] = useState<SortingState>([]);

  // Pagination state
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // Action states
  const [deletingFranchisor, setDeletingFranchisor] = useState<string | null>(null);
  const [togglingStatus, setTogglingStatus] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [franchisorsToDelete, setFranchisorsToDelete] = useState<string | null>(null);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  // Add Franchisor Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [savingFranchisor, setSavingFranchisor] = useState(false);

  // Load data function
  const loadData = useCallback(async (isInitialLoad = false) => {
    try {
      if (isInitialLoad) {
        setLoading((prev) => ({ ...prev, initial: true }));
      } else {
        setLoading((prev) => ({ ...prev, page: true }));
      }

      // Convert sorting to API format
      let sortBy = null;
      let sortOrder = null;
      if (sorting.length > 0) {
        const sort = sorting[0];
        sortBy = sort.id;
        sortOrder = sort.desc ? 'desc' : 'asc';
      }

      const filters: FranchisorsFilters = {
        skip: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        search: debouncedSearchTerm || null,
        industry: industryFilter || null,
        region: regionFilter || null,
        is_active: statusFilter,
        sort_by: sortBy,
        sort_order: sortOrder,
      };

      const response = await franchisorsService.getList(filters);
      if (response.success) {
        setFranchisors(response.data.items);
        setTotalItems(response.data.total_count);
      } else {
        toast.error("Failed to load franchisors");
      }
    } catch (error) {
      console.error("Error loading franchisors:", error);
      toast.error("Failed to load franchisors");
    } finally {
      if (isInitialLoad) {
        setLoading((prev) => ({ ...prev, initial: false }));
      } else {
        setLoading((prev) => ({ ...prev, page: false }));
      }
    }
  }, [pagination.pageIndex, pagination.pageSize, debouncedSearchTerm, industryFilter, regionFilter, statusFilter, sorting]);

  // Load categories for filter dropdown
  const loadCategories = useCallback(async () => {
    try {
      const categories = await franchisorsService.getCategories();
      setCategories(categories);
    } catch (error) {
      console.error("Error loading categories:", error);
    }
  }, []);


    // Debounce search term
    useEffect(() => {
      const timer = setTimeout(() => {
        setDebouncedSearchTerm(searchTerm);
      }, 500);
  
      return () => clearTimeout(timer);
    }, [searchTerm]);

  // Effects
  useEffect(() => {
    const initializeData = async () => {
      await Promise.all([
        loadData(true), // Initial load with full page loading
        loadCategories()
      ]);
    };

    initializeData();
  }, []);

  // Separate effect for subsequent data loads
  useEffect(() => {
    if (!loading.initial) {
      loadData();
    }
  }, [loadData, loading.initial]);

  // Action handlers
  const handleEdit = (franchisor: Franchisor) => {
    navigate(`/franchisors/${franchisor.id}/edit`);
  };

  const handleView = (franchisor: Franchisor) => {
    navigate(`/franchisors/${franchisor.id}`);
  };

  const handleDeleteRequest = (franchisorsId: string) => {
    setFranchisorsToDelete(franchisorsId);
    setShowDeleteModal(true);
  };

  // Download sample CSV template
  const downloadSampleCsv = () => {
    const sampleData = [
      {
        name: "Sample Franchise 1",
        contactFirstName: "John",
        contactLastName: "Doe",
        email: "<EMAIL>",
        phone: "+1234567890",
        region: "North America",
        budget: 50000,
        industry_id: "1" // Note: Use actual industry ID from your system
      },
      {
        name: "Sample Franchise 2",
        contactFirstName: "Jane",
        contactLastName: "Smith",
        email: "<EMAIL>",
        phone: "+1987654321",
        region: "Europe",
        budget: 75000,
        industry_id: "2"
      }
    ];

    // Convert to CSV
    const headers = Object.keys(sampleData[0]);
    const csvContent = [
      headers.join(','),
      ...sampleData.map(row => headers.map(header => `"${row[header as keyof typeof row]}"`).join(','))
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'franchisor_import_template.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Sample CSV template downloaded');
  };

  const handleDeleteConfirm = async () => {
    if (!franchisorsToDelete) return;

    try {
      setDeletingFranchisor(franchisorsToDelete);
      const success = await franchisorsService.delete(franchisorsToDelete);

      if (success) {
        toast.success("Franchisor deleted successfully");
        await loadData();
      } else {
        toast.error("Failed to delete franchisor");
      }
    } catch (error) {
      console.error("Error deleting franchisor:", error);
      toast.error("Failed to delete franchisor");
    } finally {
      setDeletingFranchisor(null);
      setShowDeleteModal(false);
      setFranchisorsToDelete(null);
    }
  };

  const handleToggleStatus = async (id: string, currentStatus: boolean) => {
    try {
      setTogglingStatus(id);
      const success = await franchisorsService.toggleStatus(id, !currentStatus);

      if (success) {
        toast.success(`Franchisor ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
        await loadData();
      } else {
        toast.error("Failed to update status");
      }
    } catch (error) {
      console.error("Error toggling status:", error);
      toast.error("Failed to update status");
    } finally {
      setTogglingStatus(null);
    }
  };



  // Define table columns with improved structure
  const columns = useMemo<ColumnDef<Franchisor, unknown>[]>(
    () => [
      {
        accessorKey: "name",
        header: "Brand Name",
        enableSorting: true,
        cell: (info) => (
          <div className="font-medium text-gray-900">
            {info.getValue() as string}
          </div>
        ),
      },
      {
        accessorKey: "franchisor_won_id",
        header: "Won ID",
        enableSorting: true,
        cell: (info) => (
          <span className="text-gray-700 font-mono text-sm">
            {(info.getValue() as string) || '-'}
          </span>
        ),
      },
      {
        accessorKey: "contactFirstName",
        id: "contactfirstname",
        header: "Contact Name",
        enableSorting: true,
        cell: (info) => {
          const firstName = info.row.original.contactFirstName;
          const lastName = info.row.original.contactLastName;
          const fullName = `${firstName || ''} ${lastName || ''}`.trim();

          return (
            <span className="text-gray-900">
              {fullName || '-'}
            </span>
          );
        },
      },
      {
        id: "contact_info",
        header: "Contact",
        enableSorting: false,
        cell: (info) => {
          const email = info.row.original.email;
          const phone = info.row.original.phone;

          return (
            <div className="space-y-1">
              {email && (
                <div className="text-sm">
                  <a
                    href={`mailto:${email}`}
                    className="text-blue-600 hover:text-blue-800 truncate"
                    title={email}
                  >
                    {email}
                  </a>
                </div>
              )}
              {phone && (
                <div className="text-sm">
                  <a
                    href={`tel:${phone}`}
                    className="text-gray-700 hover:text-gray-900"
                    title={phone}
                  >
                    {phone}
                  </a>
                </div>
              )}
              {!email && !phone && (
                <span className="text-gray-500 text-sm">No contact info</span>
              )}
            </div>
          );
        },
      },
      {
        id: "industry_name",
        accessorFn: (row) => row.industry_details?.name || "-",
        header: "Industry",
        enableSorting: false,
        cell: (info) => (
          <span className="text-gray-700">{info.getValue() as string}</span>
        ),
      },
      {
        accessorKey: "region",
        header: "Region",
        enableSorting: false,
        cell: (info) => {
          const region = info.getValue() as string;
          const displayRegion = region
            ?.replace(/_/g, " ")
            ?.replace(/\b\w/g, (l) => l.toUpperCase()) || "-";
          return (
            <span className="text-gray-700">{displayRegion}</span>
          );
        },
      },
      {
        accessorKey: "budget",
        header: "Budget",
        enableSorting: false,
        cell: (info) => (
          <span className="text-gray-700 font-medium">
            ${(info?.getValue() as number)?.toLocaleString() || 0}
          </span>
        ),
      },
      {
        accessorKey: "is_active",
        header: "Status",
        enableSorting: false,
        cell: (info) => {
          const isActive = info.getValue() as boolean ?? false;
          const id = info.row.original.id;
          const isToggling = togglingStatus === id;
          return (
            <div className="relative">
              <button
                onClick={() => handleToggleStatus(id, isActive)}
                disabled={isToggling}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isActive ? "bg-blue-600" : "bg-gray-200"
                } ${
                  isToggling
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer"
                }`}
                title={isActive ? "Deactivate" : "Activate"}
              >
                {isToggling ? (
                  <Loader2 className="w-4 h-4 animate-spin text-white mx-auto" />
                ) : (
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                      isActive ? "translate-x-6" : "translate-x-1"
                    }`}
                  />
                )}
              </button>
            </div>
          );
        },
      },
      {
        accessorKey: "created_at",
        header: "Created",
        enableSorting: true,
        cell: (info) => {
          const date = info.getValue() as string;
          return (
            <span className="text-gray-700 text-sm">
              {date ? new Date(date).toLocaleDateString() : '-'}
            </span>
          );
        },
      },
      {
        id: "actions",
        header: "Actions",
        enableSorting: false,
        cell: (info) => (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleEdit(info.row.original)}
              className="text-blue-600 hover:text-blue-800 p-1"
              title="Edit"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleView(info.row.original)}
              className="text-green-600 hover:text-green-800 p-1"
              title="View Details"
            >
              <Eye className="w-4 h-4" />
            </button>
            {/* <button
              onClick={() => handleDeleteRequest(info.row.original.id)}
              disabled={deletingFranchisor === info.row.original.id}
              className="text-red-600 hover:text-red-800 p-1 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Delete"
            >
              {deletingFranchisor === info.row.original.id ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4" />
              )}
            </button> */}
          </div>
        ),
      },
    ],
    [togglingStatus, deletingFranchisor]
  );

  // Setup table
  const table = useReactTable({
    data: franchisors,
    columns,
    pageCount: Math.ceil(totalItems / pagination.pageSize),
    state: {
      pagination: {
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
      },
      sorting,
    },
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
    enableSortingRemoval: false,
  });

  // Handle sync
  const handleSync = async () => {
    setIsSyncing(true);
    setShowSyncOverlay(true);
    setSyncSuccess(false);

    try {
      const response = await franchisorsService.syncWithZoho();
      loadData(); // Refresh the list

      // Show success state
      setSyncSuccess(true);

      toast.success(
        response.message.description || "Zoho sync completed successfully!"
      );
    } catch (error: any) {
      setShowSyncOverlay(false);
      console.error("Error syncing:", error);
      toast.error(error.message || "Failed to sync with Zoho.");
    } finally {
      setIsSyncing(false);
    }
  };

  // Handle CSV import
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      toast.error("Please select a valid CSV file");
      event.target.value = '';
      return;
    }

    try {
      setLoading((prev) => ({ ...prev, import: true }));

      const result = await franchisorsService.importFromCsv(file);

      // Show detailed import results
      const { total_rows, successful_imports, failed_imports, errors } = result;

      if (successful_imports > 0) {
        toast.success(
          `Import completed! ${successful_imports} of ${total_rows} franchisors imported successfully.`
        );

        // Show errors if any
        if (failed_imports > 0 && errors.length > 0) {
          console.warn("Import errors:", errors);
          toast.error(
            `${failed_imports} franchisors failed to import. Check console for details.`
          );
        }

        // Reload data to show new franchisors
        await loadData();
      } else {
        toast.error("No franchisors were imported. Please check your CSV format.");
      }

    } catch (error: any) {
      console.error("Error importing CSV:", error);

      // Handle specific error messages from API response
      let errorMessage = "Failed to import CSV file";

      if (error?.response?.data) {
        const responseData = error.response.data;

        // Check for different possible error message formats
        if (responseData.detail) {
          errorMessage = responseData.detail;
        } else if (responseData.message?.description) {
          errorMessage = responseData.message.description;
        } else if (responseData.message?.title) {
          errorMessage = responseData.message.title;
        } else if (responseData.message) {
          errorMessage = typeof responseData.message === 'string' ? responseData.message : errorMessage;
        } else if (responseData.error) {
          errorMessage = responseData.error;
        }
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    } finally {
      setLoading((prev) => ({ ...prev, import: false }));
      // Reset file input
      event.target.value = '';
    }
  };

  const isFilterActive = searchTerm.trim() !== "" || industryFilter !== "";

  // Show simple loading state during initial load
  if (loading.initial) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="text-gray-600">Loading franchisors...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 mt-0">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Franchisor Management
          </h1>
          <p className="text-gray-600">
            Manage your franchisor opportunities and business partnerships
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleSync}
            disabled={isSyncing}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isSyncing ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                <span>Syncing...</span>
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                <span>Sync Zoho</span>
              </>
            )}
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Franchisor
          </button>

          {/* CSV Import/Export Section */}
          <div className="flex items-center gap-2">
          

            <label
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 cursor-pointer flex items-center"
              title="Import franchisors from a CSV file. Download the sample CSV first to see the required format."
            >
              {loading.import ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Upload className="w-4 h-4 mr-2" />
              )}
              Import CSV
              <input
                type="file"
                accept=".csv"
                onChange={handleImport}
                className="hidden"
                disabled={loading.import}
              />
            </label>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search franchisors..."
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setPagination((prev) => ({ ...prev, pageIndex: 0 }));
              }}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <Select
            value={industryFilter || "all"}
            onChange={(value) => {
              setIndustryFilter(value === "all" ? "" : value);
              setPagination((prev) => ({ ...prev, pageIndex: 0 }));
            }}
            options={[
              { value: "all", label: "All Industries" },
              ...categories.map((category) => ({
                value: category.id,
                label: category.name,
              })),
            ]}
          />
          {/* Clear Filters Button */}
            <div className="w-full md:w-auto flex-none">
              <button
                onClick={() => {
                  setSearchTerm("");
                  setIndustryFilter("")
                  setPagination((prev) => ({ ...prev, pageIndex: 0 }));
                  setDebouncedSearchTerm("");
                }}
                disabled={!isFilterActive}
                className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 w-full md:w-auto justify-center relative"
              >
                <Filter className="w-4 h-4" />
                <span>Clear Filters</span>

                {isFilterActive && (
                  <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
                )}
              </button>
            </div>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {loading.page ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading franchisors...</span>
          </div>
        ) : franchisors.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg">No franchisors found</div>
            <div className="text-gray-400 text-sm mt-1">
              Try adjusting your search or filters
            </div>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <th
                          key={header.id}
                          className="text-left py-4 px-6 font-medium text-gray-900"
                        >
                          {header.isPlaceholder ? null : (
                            <div
                              className={`flex items-center space-x-2 ${
                                header.column.getCanSort()
                                  ? "cursor-pointer select-none hover:text-blue-600"
                                  : ""
                              }`}
                              onClick={header.column.getToggleSortingHandler()}
                            >
                              <span>
                                {flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                              </span>
                              {header.column.getCanSort() && (
                                <span className="flex flex-col">
                                  {header.column.getIsSorted() === "asc" ? (
                                    <ArrowUp className="w-4 h-4 text-blue-600" />
                                  ) : header.column.getIsSorted() === "desc" ? (
                                    <ArrowDown className="w-4 h-4 text-blue-600" />
                                  ) : (
                                    <ArrowUpDown className="w-4 h-4 text-gray-400" />
                                  )}
                                </span>
                              )}
                            </div>
                          )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {table.getRowModel().rows.map((row) => (
                    <tr key={row.id} className="hover:bg-gray-50">
                      {row.getVisibleCells().map((cell) => (
                        <td key={cell.id} className="py-4 px-6">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <Pagination
              pageIndex={pagination.pageIndex}
              pageSize={pagination.pageSize}
              totalCount={totalItems}
              pageCount={table.getPageCount()}
              onPageChange={(page) =>
                setPagination((prev) => ({ ...prev, pageIndex: page }))
              }
              onPrevious={() => table.previousPage()}
              onNext={() => table.nextPage()}
              canPreviousPage={table.getCanPreviousPage()}
              canNextPage={table.getCanNextPage()}
            />
          </>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        open={showDeleteModal}
        onCancel={() => {
          setShowDeleteModal(false);
          setFranchisorsToDelete(null);
        }}
        onConfirm={handleDeleteConfirm}
        title="Delete Franchisor"
        message="Are you sure you want to delete this franchisor? This action cannot be undone."
        loading={!!deletingFranchisor}
      />

      {/* Add Franchisor Modal */}
      {showAddModal && (
        <AddFranchisorModal
          onClose={() => setShowAddModal(false)}
          onSuccess={() => {
            setShowAddModal(false);
            loadData(); // Refresh the list
          }}
          categories={categories}
          loading={savingFranchisor}
          setLoading={setSavingFranchisor}
        />
      )}

      {/* Loading Overlay for Sync */}
      <LoadingOverlay
        isVisible={showSyncOverlay}
        message="Setting things up — this won't take long."
        showSuccess={syncSuccess}
        successMessage="Zoho sync completed successfully!"
        onComplete={() => {
          setShowSyncOverlay(false);
          setSyncSuccess(false);
        }}
      />
    </div>
  );
};

// Add Franchisor Modal Component
interface AddFranchisorModalProps {
  onClose: () => void;
  onSuccess: () => void;
  categories: CategoryOption[];
  loading: boolean;
  setLoading: (loading: boolean) => void;
}

const AddFranchisorModal: React.FC<AddFranchisorModalProps> = ({
  onClose,
  onSuccess,
  categories,
  loading,
  setLoading,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    contactFirstName: '',
    contactLastName: '',
    email: '',
    phone: '',
    region: '',
    budget: 0,
    industry_id: '',
  });

  const [brochureFile, setBrochureFile] = useState<File | null>(null);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleClose = () => {
    // Reset form data and brochure file when closing
    setFormData({
      name: '',
      contactFirstName: '',
      contactLastName: '',
      email: '',
      phone: '',
      region: '',
      budget: 0,
      industry_id: '',
    });
    setBrochureFile(null);
    onClose();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Brand name is required');
      return;
    }

    setLoading(true);
    try {
      // Step 1: Create the franchisor
      const createdFranchisor = await franchisorsService.create(formData);

      // Step 2: Upload brochure if provided
      if (brochureFile && createdFranchisor.id) {
        try {
          await franchisorsService.uploadBrochure(createdFranchisor.id, brochureFile);
          toast.success('Franchisor created successfully with brochure');
        } catch (brochureError) {
          console.error('Error uploading brochure:', brochureError);
          toast.success('Franchisor created successfully, but brochure upload failed');
        }
      } else {
        toast.success('Franchisor created successfully');
      }

      onSuccess();
      handleClose();
    } catch (error) {
      console.error('Error creating franchisor:', error);
      toast.error('Failed to create franchisor');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Add New Franchisor</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
              disabled={loading}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Brand Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter brand name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Industry
                </label>
                <Select
                  options={categories.map((c) => ({ value: c.id, label: c.name }))}
                  value={formData.industry_id}
                  onChange={(value) => handleInputChange('industry_id', value)}
                  placeholder="Select Industry"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact First Name
                </label>
                <input
                  type="text"
                  value={formData.contactFirstName}
                  onChange={(e) => handleInputChange('contactFirstName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter first name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Last Name
                </label>
                <input
                  type="text"
                  value={formData.contactLastName}
                  onChange={(e) => handleInputChange('contactLastName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter last name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter email address"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter phone number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Region
                </label>
                <Select
                  value={formData.region}
                  onChange={(v) => handleInputChange('region', v)}
                  placeholder="Select Region"
                  options={[
                    { value: 'north_america', label: 'North America' },
                    { value: 'europe', label: 'Europe' },
                    { value: 'asia_pacific', label: 'Asia Pacific' },
                    { value: 'africa', label: 'Africa' },
                    { value: 'south_america', label: 'South America' },
                  ]}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Budget
                </label>
                <input
                  type="number"
                  value={formData.budget}
                  onChange={(e) => handleInputChange('budget', Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter budget amount"
                  min="0"
                />
              </div>
            </div>

            {/* Brochure Upload Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Brochure (Optional)
              </label>
              <input
                type="file"
                onChange={(e) => setBrochureFile(e.target.files?.[0] || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                accept=".pdf"
              />
              <p className="text-xs text-gray-500 mt-1">
                Supported format: PDF only
              </p>
            </div>

            <div className="flex justify-end space-x-3 pt-6">
              <button
                type="button"
                onClick={handleClose}
                disabled={loading}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || !formData.name.trim()}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>{brochureFile ? 'Creating & Uploading...' : 'Creating...'}</span>
                  </>
                ) : (
                  <>
                    <span>Create Franchisor</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Brands;