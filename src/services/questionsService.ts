import api from "./api";

export interface QuestionItem {
  id: string;
  franchisor_id: string;
  franchisor_name: string;
  industry: string;
  question_text: string;
  question_type: string;
  order: number;
  isActive: boolean;
  created_at: string;
  expected_answer?: string[];
  expected_answer_type?: string;
  answer_options?: string[];
  score_weight?: number;
  qualification_weight?: number;
  passing_criteria?: string | string[];
  validation_rules?: string;
  requires_follow_up?: boolean;
  follow_up_logic?: string;
  is_required?: boolean;
  category?: string;
}

export interface QuestionsListResponse {
  success: boolean;
  status: string;
  message: {
    title: string;
    description: string;
  };
  data: QuestionItem[];
  metadata: {
    timestamp: string;
    request_id: string | null;
    api_version: string;
    processing_time_ms: number | null;
  };
  error_code: string | null;
}

export interface QuestionsFilters {
  page: number;
  size: number;
  search: string | null;
  franchisor_id: string | null;
  is_active: boolean | null;
  sortBy?: string | null,
  sortOrder?: string | null
}

// Create request body
export interface CreateQuestionInput {
  franchisor_id: string;
  question_text: string;
  question_type: string;
  expected_answer?: string[];
  expected_answer_type?: string;
  answer_options?: string;
  score_weight?: number;
  qualification_weight?: number;
  passing_criteria?: string;
  validation_rules?: string;
  requires_follow_up?: boolean;
  follow_up_logic?: string;
  is_required?: boolean;
  category?: string;
}

// Response structure for question creation
export interface QuestionCreationItem {
  details: {
    id: string;
    franchisor_id: string;
    franchisor_name: string;
    industry: string;
    question_text: string;
    question_internal_text: string;
    question_type: string;
    order_sequence: number;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
}

export interface QuestionCreationResponse {
  success: boolean;
  message: { title: string; description: string };
  data: QuestionCreationItem;
  error_code: string | null;
}

// Toggle response
export interface QuestionToggleStatusResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: {
    details: {
      id: string;
      franchisor_id: string;
      franchisor_name: string;
      category: string;
      question_text: string;
      question_internal_text: string;
      question_type: string;
      order_sequence: number;
      is_active: boolean;
      created_at: string;
      updated_at: string;
    };
  };
  error_code: string | null;
}

export interface FranchisorDropdown {
  id: string;
  name: string;
  industry: string;
}

// Interface for the details object containing franchisors array
export interface FranchisorsDetails {
  franchisors: FranchisorDropdown[];
}

export interface FranchisorsDropdownResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: {
    details: {
      franchisors: FranchisorDropdown[];
    };
  };
}

export interface FranchisorQuestionsResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: {
    details: {
      questions: {
        id: string;
        franchisor_id: string;
        franchisor_name: string;
        industry: string;
        question_text: string;
        question_internal_text: string;
        question_type: string;
        order: number;
        is_active: boolean;
        created_at: string;
        updated_at: string;
      }[];
    };
  };
  error_code: string | null;
}

interface QuestionOrderUpdate {
  question_id: string;
  order_sequence: number;
}

interface ReorderQuestionsPayload {
  franchisor_id: string;
  questions: QuestionOrderUpdate[];
}

interface ReorderQuestionsResponse {
  success: boolean;
  title: string;
  description: string;
  details: {
    franchisor_id: string;
    reordered_questions: number;
  };
  error_code: string | null;
}

export const questionsService = {
  /**
   * GET /questions/list
   */
  getList: async (
    filters: QuestionsFilters
  ): Promise<QuestionsListResponse> => {
    const params = new URLSearchParams();

    params.append("page", filters.page.toString());
    params.append("size", filters.size.toString());

    if (filters.search?.trim()) {
      params.append("search", filters.search.trim());
    }

    if (filters.franchisor_id?.trim()) {
      params.append("franchisor_id", filters.franchisor_id.trim());
    }

    if (filters.is_active !== null && filters.is_active !== undefined) {
      params.append("is_active", filters.is_active.toString());
    }

    if (filters.sortBy) params.append("sort_by", filters.sortBy === "text" ? "question_text" : filters.sortBy);
    if (filters.sortOrder) params.append("sort_order", filters.sortOrder);

    const url = `/questions/list?${params.toString()}`;

    const response = await api.get<QuestionsListResponse>(url);
    return response.data;
  },

  /**
   * POST /questions/create
   */
  create: async (
    input: CreateQuestionInput
  ): Promise<QuestionCreationResponse> => {
    const response = await api.post<QuestionCreationResponse>(
      "/questions/create",
      input
    );
    return response.data;
  },

  /**
   * PUT /api/questions/{question_id}/update
   * Update an existing question
   */
  update: async (
    question_id: string,
    data: any
  ): Promise<QuestionCreationResponse> => {
    const response = await api.put<QuestionCreationResponse>(
      `/questions/${question_id}/update`,
      data,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    return response.data;
  },

  /**
   * DELETE /questions/{question_id}/delete
   * Delete a question by ID
   */
  delete: async (question_id: string): Promise<boolean> => {
    const response = await api.delete<{
      success: boolean;
      message: { title: string; description: string };
      data: { details : {question_id: string; deleted_order: number; franchisor_id: string; reordered: boolean }};
    }>(`/questions/${question_id}/delete`);
    return response.data.success;
  },

  /**
   * PATCH /questions/{question_id}/toggle-status
   * Toggle question active status
   */
  toggleStatus: async (
    question_id: string
  ): Promise<QuestionToggleStatusResponse> => {
    const response = await api.patch<QuestionToggleStatusResponse>(
      `/questions/${question_id}/toggle-status`
    );
    return response.data;
  },

  /**
   * GET /questions/franchisors/dropdown
   * Get franchisors for dropdown
   */
  getFranchisorsDropdown: async (): Promise<FranchisorsDropdownResponse> => {
    const response = await api.get<FranchisorsDropdownResponse>(
      "/questions/franchisors/dropdown"
    );
    return response.data;
  },

  /**
   * GET /questions/franchisor/{franchisor_id}
   * Get questions by franchisor ID
   */
  getQuestionByFranchisorId: async (
    franchisor_id: string
  ): Promise<FranchisorQuestionsResponse> => {
    const response = await api.get<FranchisorQuestionsResponse>(
      `/questions/franchisor/${franchisor_id}`
    );
    return response.data;
  },

  updateQuestionOrder: async (
    franchisor_id: string,
    questions: QuestionOrderUpdate[]
  ): Promise<ReorderQuestionsResponse> => {
    const payload: ReorderQuestionsPayload = {
      franchisor_id,
      questions
    };

    const response = await api.post<ReorderQuestionsResponse>(
      "/questions/reorder",
      payload
    );
    return response.data;
  },

  getAllQuestionBanks: async (params: {
    lead_id?: string | null;
    franchisor_id?: string | null;
    search?: string | null;
    sort_by?: "name" | "created_at";
    sort_order?: "asc" | "desc";
    page?: number;
    page_size?: number;
  }) => {
    const response = await api.get("/questions_Module/question-bank", {
      params,
    });
    return response.data;
  },

  /**
   * GET /questions_Module/question-bank
   * Get question bank items (display only)
   * @param franchisor_id - Optional franchisor ID to filter questions
   */
  getQuestionBank: async (franchisor_id?: string): Promise<{
    success: boolean;
    status: string;
    message: {
      title: string;
      description: string;
    };
    data: {
      items: {
        id: string;
        name: string;
        lead_id: string;
        franchisor_id: string | null;
        is_deleted: boolean;
        is_active: boolean;
        created_at: string;
        updated_at: string;
      }[];
      total_count: number;
    };
  }> => {
    const params = new URLSearchParams();

    if (franchisor_id) {
      params.append("franchisor_id", franchisor_id);
    }

    const url = `/questions_Module/question-bank${params.toString() ? `?${params.toString()}` : ''}`;

    const response = await api.get<{
      success: boolean;
      status: string;
      message: {
        title: string;
        description: string;
      };
      data: {
        items: {
          id: string;
          name: string;
          lead_id: string;
          franchisor_id: string | null;
          is_deleted: boolean;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        }[];
        total_count: number;
      };
    }>(url);
    return response.data;
  },
};
