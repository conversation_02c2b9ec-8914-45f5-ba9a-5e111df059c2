import { useNavigate } from "react-router-dom";
import ForgotPasswordLayout from "./ForgotPasswordLayout";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { Mail } from "lucide-react";
import toast from "react-hot-toast";
import { APIErrorResponse, authService } from "../../services/authService"; // ✅ import your service
import { AxiosError } from "axios";

const schema = {
  email: Yup.object().shape({
    email: Yup.string()
      .required("Email is required")
      .email("Invalid email format"),
  }),
};

const ForgotPassword = () => {
  const navigate = useNavigate();


  const handleSubmit = async (values: { email: string }) => {
    try {
      const response = await authService.initiateForgotPassword(values.email);

      if (response?.success && response.data) {
        toast.success(response.message?.description || "OTP sent successfully");
        localStorage.setItem("forgot_password_user_id", response.data?.details?.user_id);
        navigate("/verify-otp");
      } else {
        toast.error(response.message?.description || "Failed to send OTP");
      }
    } catch (err: unknown) {
      const error = err as AxiosError<APIErrorResponse>;

      console.error(error);
      const errorMsg =
        error?.response?.data?.error?.message?.description ||
        error?.response?.data?.message?.description ||
        "Something went wrong";
      toast.error(errorMsg);
    }
  };

  return (
    <ForgotPasswordLayout
      title="Forgot Password"
      description="Enter your email to receive a verification code"
    >
      <Formik
        initialValues={{ email: "" }}
        validationSchema={schema.email}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-2">
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Field
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg"
                />
              </div>
              <ErrorMessage
                name="email"
                component="div"
                className="text-red-500 text-sm mt-1"
              />
            </div>
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 px-4 rounded-lg font-medium hover:from-yellow-700 hover:to-yellow-700 focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? "Sending..." : "Continue"}
            </button>
          </Form>
        )}
      </Formik>
    </ForgotPasswordLayout>
  );
};

export default ForgotPassword;
