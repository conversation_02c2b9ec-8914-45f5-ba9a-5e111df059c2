import React, { useState, useEffect } from 'react';
import { Loader2, CheckCircle } from 'lucide-react';

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  onComplete?: () => void;
  showSuccess?: boolean;
  successMessage?: string;
  successDuration?: number; // Duration to show success state in milliseconds
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  message = "Setting things up — this won't take long.",
  onComplete,
  showSuccess = false,
  successMessage = "All set!",
  successDuration = 2000,
}) => {
  const [showSuccessState, setShowSuccessState] = useState(false);

  useEffect(() => {
    if (showSuccess && isVisible) {
      setShowSuccessState(true);
      
      const timer = setTimeout(() => {
        setShowSuccessState(false);
        onComplete?.();
      }, successDuration);

      return () => clearTimeout(timer);
    } else {
      setShowSuccessState(false);
    }
  }, [showSuccess, isVisible, successDuration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop with blur */}
      <div className="absolute inset-0 bg-black/20 backdrop-blur-sm" />
      
      {/* Loading content */}
      <div className="relative z-10 flex flex-col items-center justify-center p-8">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 text-center">
          {showSuccessState ? (
            // Success state
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center animate-pulse">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-green-800 mb-2">
                  Success!
                </h3>
                <p className="text-green-600 text-sm">
                  {successMessage}
                </p>
              </div>
            </div>
          ) : (
            // Loading state
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  Please wait...
                </h3>
                <p className="text-gray-600 text-sm">
                  {message}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoadingOverlay;
