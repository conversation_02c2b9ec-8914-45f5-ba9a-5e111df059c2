import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Loader2,
  Calendar,
  FileText,
  ArrowUp,
  ArrowDown,
  ArrowUpDown,
} from "lucide-react";
import {
  categoryService,
  Category as CategoryType,
} from "../../services/categoryService";
import toast from "react-hot-toast";
import debounce from "lodash.debounce";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
  SortingState,
  getSortedRowModel,
} from "@tanstack/react-table";
import Pagination from "../common/Pagination";

const Industry: React.FC = () => {
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [loading, setLoading] = useState(true);
  const [tableLoading, setTableLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<CategoryType | null>(null);
  const [viewingCategory, setViewingCategory] = useState<CategoryType | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [togglingStatus, setTogglingStatus] = useState<string | null>(null);
  const [savingCategory, setSavingCategory] = useState(false);
  const [deletingCategory, setDeletingCategory] = useState<string | null>(null);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
  const [totalItems, setTotalItems] = useState(0);
  const [sorting, setSorting] = useState<SortingState>([]);

  // Non-debounced fetch for initial load and CRUD
  const fetchCategories = useCallback(
    async (
      params: { skip?: number; limit?: number; search?: string; sortBy?: string | null;
      sortOrder?: string | null; } = {
        skip: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        search: "",
      }
    ) => {
      if(!loading){
        setTableLoading(true);
      }
      try {
        const { items, total_count } = await categoryService.getCategories(
          pagination.pageIndex * pagination.pageSize,
          pagination.pageSize,
          params.search ?? "",
          params.sortBy ?? null,
          params.sortOrder ?? null
        );        
        setCategories(items);
        const totalPages = Math.ceil(total_count / pagination.pageSize);
        if (pagination.pageIndex >= totalPages && totalPages > 0) {
          setPagination((prev) => ({ ...prev, pageIndex: totalPages - 1 }));
        }
        setTotalItems(total_count);
      } catch (err) {
        toast.error("Failed to load industries.");
        setPagination(prev => ({ ...prev, pageIndex: 0 }));
      } finally {
        setLoading(false);
        if(!loading){
        setTableLoading(false);
      }
      }
    },
    [pagination.pageIndex,
  pagination.pageSize,]
  );

  // Debounced fetch for search/filter
  const debouncedFetchCategories = useRef(
    debounce(
     async (params: {
      skip?: number;
      limit?: number;
      search?: string;
      sortBy?: string | null;
      sortOrder?: string | null;
    }) => {
        setTableLoading(true);
        try {
          const { items, total_count } = await categoryService.getCategories(
            pagination.pageIndex * pagination.pageSize,
            pagination.pageSize,
            params.search ?? "",
            params.sortBy,
            params.sortOrder
          );

          setCategories(items);
          setTotalItems(total_count);
          const totalPages = Math.ceil(total_count / pagination.pageSize);
          if (pagination.pageIndex >= totalPages && totalPages > 0) {
            setPagination((prev) => ({ ...prev, pageIndex: totalPages - 1 }));
          }
          // setPagination((prev) => ({
          //   ...prev,
          //   pageIndex: serverPagination.currentPage - 1, // Convert to 0-based index
          //   pageSize: serverPagination.itemsPerPage,
          // }));
        } catch (err) {
          toast.error("Failed to load industries.");
        } finally {
          setTableLoading(false);
        }
      },
      300
    )
  ).current;

useEffect(() => {
  // Get the current sort state (first sort if multiple exist)
  const sort = sorting[0];
  
  // Prepare all parameters for the API call
  const sortParams = {
    skip: pagination.pageIndex * pagination.pageSize,
    limit: pagination.pageSize,
    search: searchTerm,
    sortBy: sort?.id || null,       // Which column to sort by
    sortOrder: sort?.desc ? 'desc' : 'asc'  // Sort direction
  };

  // Use debounced fetch for search to prevent rapid API calls
  if (searchTerm) {
    debouncedFetchCategories(sortParams);
  } else {
    // Regular fetch for other changes
    fetchCategories(sortParams);
  }
}, [sorting, searchTerm, pagination.pageIndex, pagination.pageSize, fetchCategories]);

// Cancel debounce on unmount
useEffect(() => {
  return () => {
    debouncedFetchCategories.cancel();
  };
}, []);

  const handleEdit = useCallback((category: CategoryType) => {
    setEditingCategory(category);
    setShowModal(true);
  }, []);

  const handleView = useCallback(async (category: CategoryType) => {
    try {
      const fullCategory = await categoryService.getCategoryById(category.id);
      setViewingCategory(fullCategory);
    } catch (err) {
      toast.error("Failed to load industry details.");
    }
  }, []);

  const handleDelete = useCallback(async (id: string) => {
    if (confirm("Are you sure you want to delete this industry?")) {
      try {
        setDeletingCategory(id);
        const success = await categoryService.deleteCategory(id);
        if (success) {
          setCategories((prevCategories) =>
            prevCategories.filter((c) => c.id !== id)
          );
          toast.success("Industry deleted successfully!");
        } else {
          toast.error("Failed to delete industry. Please try again.");
        }
      } catch (err) {
        console.error("Failed to delete industry:", err);
        toast.error("Failed to delete industry. Please try again.");
      } finally {
        setDeletingCategory(null);
      }
    }
  }, []);

  const handleSave = useCallback(
    async (categoryData: {
      name: string;
      description: string;
      is_active: boolean;
    }) => {
      try {
        setSavingCategory(true);
        if (editingCategory) {
          // Update existing industry
          const updatedCategory = await categoryService.updateCategory(
            editingCategory.id,
            categoryData
          );
          setCategories((prevCategories) =>
            prevCategories.map((c) =>
              c.id === updatedCategory.id ? updatedCategory : c
            )
          );
          toast.success("Industry updated successfully!");
        } else {
          // Add new industry
          const newCategory = await categoryService.createCategory(
            categoryData
          );
          setCategories((prevCategories) => [...prevCategories, newCategory]);
          toast.success("Industry created successfully!");
        }
        setShowModal(false);
      } catch (err) {
        console.error("Failed to save industry:", err);
        toast.error("Failed to save industry. Please try again.");
      } finally {
        setSavingCategory(false);
      }
    },
    [editingCategory]
  );

  const handleToggleStatus = useCallback(
    async (id: string, currentStatus: boolean) => {
      try {
        setTogglingStatus(id);
        const updatedCategory = await categoryService.toggleCategoryStatus(
          id,
          !currentStatus
        );
        setCategories((prevCategories) =>
          prevCategories.map((c) =>
            c.id === updatedCategory.id ? updatedCategory : c
          )
        );
        setViewingCategory(prev => 
          prev?.id === id ? updatedCategory : prev
        );
        toast.success(
          `Industry ${
            !currentStatus ? "activated" : "deactivated"
          } successfully!`
        );
      } catch (err) {
        console.error("Failed to toggle industry status:", err);
        toast.error("Failed to update industry status. Please try again.");
      } finally {
        setTogglingStatus(null);
      }
    },
    []
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
      setPagination(prev => ({ ...prev, pageIndex: 0 }));
    },
    []
  );

  const handleAddCategory = useCallback(() => {
    setEditingCategory(null);
    setShowModal(true);
  }, []);

  // TanStack Table columns
  const columns = React.useMemo<ColumnDef<CategoryType, unknown>[]>(
    () => [
      {
        accessorKey: "name",
        header: "Industry Name",
        enableSorting: true,
        cell: (info) => (
          <div>
            <div className="font-medium text-gray-900">
              {info.row.original.name || "Untitled Industry"}
            </div>
          </div>
        ),
      },
      {
        accessorKey: "description",
        header: "Description",
        cell: (info) => (
          <span className="text-gray-700">{info.row.original.description || "No Description"}</span>
        ),
      },
      {
        accessorKey: "is_active",
        header: "Active",
        cell: (info) => {
          const category = info.row.original;
          return (
            <div className="flex items-center space-x-3">
              <button
                onClick={() =>
                  handleToggleStatus(category.id, category.is_active)
                }
                disabled={togglingStatus === category.id}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  category.is_active ? "bg-blue-600" : "bg-gray-200"
                } ${
                  togglingStatus === category.id
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    category.is_active ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          );
        },
      },
      {
        accessorKey: "created_at",
        header: "Created Date",
        enableSorting: true,
        cell: (info) => (
          <span className="text-gray-700">
            {new Date(info.row.original.created_at)?.toLocaleDateString() || 'N/A'}
          </span>
        ),
      },
      {
        id: "actions",
        header: "Actions",
        cell: (info) => {
          const category = info.row.original;
          return (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleEdit(category)}
                className="text-blue-600 hover:text-blue-800 p-1"
                title="Edit"
              >
                <Edit className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleView(category)}
                className="text-green-600 hover:text-green-800 p-1"
                title="View Details"
              >
                <Eye className="w-4 h-4" />
              </button>
              {/* <button
                onClick={() => handleDelete(category.id)}
                disabled={deletingCategory === category.id}
                className={`text-red-600 hover:text-red-800 p-1 ${
                  deletingCategory === category.id ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                title="Delete"
              >
                {deletingCategory === category.id ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
              </button> */}
            </div>
          );
        },
      },
    ],
    [togglingStatus, deletingCategory, handleEdit]
  );

  const table = useReactTable({
    data: categories,
    columns,
    pageCount: totalItems > 0 ? Math.ceil(totalItems / pagination.pageSize) : 1,
    state: {
      pagination: {
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
      },
      sorting,
    },
    onPaginationChange: (updater) => {
      const newPagination =
        typeof updater === "function"
          ? updater({
              pageIndex: pagination.pageIndex,
              pageSize: pagination.pageSize,
            })
          : updater;

      setPagination(newPagination);
      // fetchCategories({
      //   skip: newPagination.pageIndex * newPagination.pageSize,
      //   limit: newPagination.pageSize,
      // });
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
    enableSortingRemoval: false,
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading industries...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Industries Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your business industries and classifications
          </p>
        </div>
        <button
          onClick={handleAddCategory}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Industry</span>
        </button>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search industries..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          {/* <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
            <Filter className="w-4 h-4" />
            <span>Filter</span>
          </button> */}
        </div>
      </div>

      {/* Industries List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 relative">
        {tableLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            <span className="ml-2 text-gray-600">Loading industries...</span>
          </div>
        ) : categories.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg">No industries found</div>
            <div className="text-gray-400 text-sm mt-1">
              Try adjusting your search or filters
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                {table.getHeaderGroups().map((headerGroup) => (
                  <tr key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      const isSortable =
                        header.column.id === "name" ||
                        header.column.id === "created_at";
                      return (
                        <th
                          key={header.id}
                          onClick={isSortable
                              ? header.column.getToggleSortingHandler()
                              : undefined
                          }
                          className={`text-left py-4 px-6 font-medium text-gray-900 ${
                            isSortable
                              ? "cursor-pointer select-none"
                              : ""
                          }`}
                        >
                          <div className="flex items-center gap-1 space-x-1">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {isSortable && (
                              <>
                                {header.column.getIsSorted() === "asc" ? (
                                  <ArrowUp className="w-4 h-4 text-blue-600" />
                                ) : header.column.getIsSorted() === "desc" ? (
                                  <ArrowDown className="w-4 h-4 text-blue-600" />
                                ) : (
                                  <ArrowUpDown className="w-4 h-4 text-gray-400" />
                                )}
                              </>
                            )}
                          </div>
                        </th>
                      );
                    })}
                  </tr>
                ))}
              </thead>
              <tbody className="divide-y divide-gray-200">
                {table.getRowModel().rows.map((row) => (
                  <tr key={row.id} className="hover:bg-gray-50">
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className="py-4 px-6">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
            {/* Pagination Controls */}
            <Pagination
              pageIndex={pagination.pageIndex}
              pageSize={pagination.pageSize}
              totalCount={totalItems}
              pageCount={table.getPageCount()}
              onPageChange={(page) => setPagination((prev) => ({ ...prev, pageIndex: page }))}
              onPrevious={() => table.previousPage()}
              onNext={() => table.nextPage()}
              canPreviousPage={table.getCanPreviousPage()}
              canNextPage={table.getCanNextPage()}
            />
          </div>
        )}
      </div>

      {/* Add/Edit Modal */}
      {showModal && (
        <CategoryModal
          category={editingCategory}
          onClose={() => setShowModal(false)}
          onSave={handleSave}
          saving={savingCategory}
        />
      )}

      {/* View Details Modal */}
      {viewingCategory && (
        <CategoryDetailsModal
          category={viewingCategory}
          onClose={() => setViewingCategory(null)}
          onToggleStatus={handleToggleStatus}
          togglingStatus={togglingStatus}
        />
      )}
    </div>
  );
};

const CategoryModal: React.FC<{
  category: CategoryType | null;
  onClose: () => void;
  onSave: (categoryData: { name: string; description: string; is_active: boolean }) => void;
  saving: boolean;
}> = ({ category, onClose, onSave, saving }) => {
  const [formData, setFormData] = useState({
    name: category?.name || '',
    description: category?.description || '',
    is_active: category?.is_active ?? true
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {category ? 'Edit Industry' : 'Add New Industry'}
          </h2>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Industry Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              disabled={saving}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              disabled={saving}
            />
          </div>
          
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              disabled={saving}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>{category ? 'Updating...' : 'Creating...'}</span>
                </>
              ) : (
                <span>{category ? 'Update' : 'Create'}</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// New component for viewing industry details
const CategoryDetailsModal: React.FC<{
  category: CategoryType;
  onClose: () => void;
  onToggleStatus: (id: string, currentStatus: boolean) => void;
  togglingStatus: string | null;
}> = ({ category, onClose, onToggleStatus, togglingStatus }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 !mt-0">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Industry Details</h2>
        </div>
        
        <div className="p-6">
          {/* Category Header */}
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{category.name}</h1>
              <p className="text-sm text-gray-500 mt-1">ID: {category.id}</p>
            </div>
            <div className="mt-4 md:mt-0">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => onToggleStatus(category.id, category.is_active)}
                  disabled={togglingStatus === category.id}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                    category.is_active ? 'bg-blue-600' : 'bg-gray-200'
                  } ${togglingStatus === category.id ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      category.is_active ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
          
          {/* Category Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <FileText className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">Description</h3>
              </div>
              <p className="text-gray-700 ml-8">{category.description}</p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <Calendar className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">Created Date</h3>
              </div>
              <p className="text-gray-700 ml-8">{new Date(category.created_at).toLocaleDateString()}</p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-3 mb-2">
                <Calendar className="w-5 h-5 text-gray-500" />
                <h3 className="font-medium text-gray-900">Last Updated</h3>
              </div>
              <p className="text-gray-700 ml-8">{new Date(category.updated_at).toLocaleDateString()}</p>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Close
            </button>
          
          </div>
        </div>
      </div>
    </div>
  );
};

export default Industry; 