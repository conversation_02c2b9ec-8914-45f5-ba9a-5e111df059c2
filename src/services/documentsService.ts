// services/documentsService.ts

import api from "./api";

/**
 * Interfaces
 */

// Document entity returned by API
export interface DocumentItem {
  id: string;
  name: string;
  description: string;
  file_type: string;
  file_size: string;
  file_path: string;
  file_url: string;
  user_id: string;
  franchisor_id: string;
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
}

// Pagination metadata
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

// Response structure for list
export interface DocumentListResponse {
  success: boolean;
  message: { title: string; description: string };
  data: {
    items: DocumentItem[];
    total_count: number;
    pagination: PaginationInfo;
  };
}

// Response structure for single document
export interface DocumentResponse {
  success: boolean;
  message: { title: string; description: string };
  data: DocumentItem;
}

// Create/Update request body
export interface CreateOrUpdateDocumentInput {
  name: string;
  description: string;
  file_type: string;
  file_size: string;
  file_path?: string; // Optional during update
  user_id: string;
  franchisor_id: string;
  is_active: boolean;
}

// Upload (file-based) request body
export interface UploadDocumentInput {
  file: File;
  name: string;
  description: string | null;
  franchisor_id?: string;
  is_active?: boolean;
}

// Bulk upload response
export interface BulkUploadResponse {
  success: boolean;
  message: { title: string; description: string };
  data: {
    uploaded_documents: {
      id: string;
      name: string;
      file_url: string;
      franchisor_id: string;
    }[];
    failed_uploads: [];
    total_uploaded: number;
    total_failed: number;
  };
}

// Bulk delete request
export interface BulkDeleteInput {
  document_ids: string[];
}

// Bulk delete response
export interface BulkDeleteResponse {
  success: boolean;
  message: { title: string; description: string };
  data: {
    deleted_count: number;
    requested_count: number;
  };
}

// Filters for list
export interface DocumentFilters {
  skip?: number;
  limit?: number;
  search?: string;
  franchisor_id?: string;
  file_type?: string;
  is_active?: boolean | null;
  sortBy?: string | null,
  sortOrder?: string | null
}

/**
 * Document Service Methods
 */
export const documentsService = {
  /**
   * GET /documents
   */
  getList: async (filters: DocumentFilters): Promise<DocumentListResponse> => {
    const params = new URLSearchParams();
    if (filters.skip !== undefined)
      params.append("skip", filters.skip.toString());
    if (filters.limit !== undefined)
      params.append("limit", filters.limit.toString());
    if (filters.search) params.append("search", filters.search);
    if (filters.franchisor_id)
      params.append("franchisor_id", filters.franchisor_id);
    if (filters.file_type) params.append("file_type", filters.file_type);
    if (filters.is_active !== null && filters.is_active !== undefined)
      params.append("is_active", filters.is_active.toString());
    if (filters.sortBy) params.append("sort_by", filters.sortBy);
    if (filters.sortOrder) params.append("sort_order", filters.sortOrder);

    const response = await api.get<DocumentListResponse>(
      `/documents/?${params}`
    );
    return response.data;
  },

  /**
   * GET /documents/:id
   */
  getById: async (id: string): Promise<DocumentItem> => {
    const response = await api.get<DocumentResponse>(`/documents/${id}`);
    return response.data.data;
  },

  /**
   * POST /documents
   */
  create: async (input: CreateOrUpdateDocumentInput): Promise<DocumentItem> => {
    const response = await api.post<DocumentResponse>("/documents/", input);
    return response.data.data;
  },

  /**
   * PUT /documents/:id
   */
  update: async (
    id: string,
    input: CreateOrUpdateDocumentInput
  ): Promise<DocumentItem> => {
    const response = await api.put<DocumentResponse>(`/documents/${id}`, input);
    return response.data.data;
  },

  /**
   * DELETE /documents/:id
   */
  delete: async (id: string): Promise<boolean> => {
    const response = await api.delete<{
      success: boolean;
      message: { title: string; description: string };
      data: { document_id: string; deleted: boolean };
    }>(`/documents/${id}`);
    return response.data.data.deleted;
  },

  /**
   * PATCH /documents/:id/status
   */
  updateStatus: async (id: string, is_active: boolean): Promise<boolean> => {
    const response = await api.patch<DocumentResponse>(
      `/documents/${id}/status`,
      {
        is_active,
      }
    );
    return response.data.success;
  },

  /**
   * POST /documents/upload (single file upload)
   */
  uploadFile: async (payload: UploadDocumentInput): Promise<DocumentItem> => {
    const formData = new FormData();
    formData.append("file", payload.file);
    formData.append("name", payload.name);
    if (payload.description)
      formData.append("description", payload.description);
    if (payload.franchisor_id)
      formData.append("franchisor_id", payload.franchisor_id);
    if (payload.is_active !== undefined)
      formData.append("is_active", payload.is_active.toString());

    const response = await api.post<DocumentResponse>(
      "/documents/upload",
      formData,
      {
        headers: { "Content-Type": "multipart/form-data" },
      }
    );

    return response.data.data;
  },

  /**
   * POST /documents/bulk-upload
   */
    bulkUpload: async (
    files: File[],
    franchisor_id: string | null,
    is_active: boolean
  ): Promise<BulkUploadResponse> => {
    const formData = new FormData();
    files.forEach((file) => formData.append("files", file));
    if (franchisor_id) formData.append("franchisor_id", franchisor_id);
    formData.append("is_active", String(is_active));
    const response = await api.post<BulkUploadResponse>(
      "/documents/bulk-upload",
      formData,
      {
        headers: { "Content-Type": "multipart/form-data" },
      }
    );
    return response.data;
  },

  /**
   * POST /documents/bulk-delete
   */
  bulkDelete: async (input: BulkDeleteInput): Promise<BulkDeleteResponse> => {
    const response = await api.post<BulkDeleteResponse>(
      "/documents/bulk-delete",
      input
    );
    return response.data;
  },
};
