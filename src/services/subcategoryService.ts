import api from "./api";

export interface Subcategory {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  is_deleted: boolean;
  category_id: string;
  created_at: string;
  updated_at: string;
}

export interface SubcategoryResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: {
    items: Subcategory[];
    total_count: number;
    pagination: {
      current_page: number;
      total_pages: number;
      items_per_page: number;
      total_items: number;
    };
  };
  metadata: {
    timestamp: string;
    request_id: string | null;
    api_version: string;
    processing_time_ms: number | null;
  };
  error_code: number | null;
}

export interface SingleSubcategoryResponse {
  success: boolean;
  message: {
    title: string;
    description: string;
  };
  data: {
    details: [string, any][];
    pagination: any;
  };
  error_code: number;
}

export interface CreateSubcategoryRequest {
  name: string;
  description: string;
  is_active: boolean;
  is_deleted?: boolean;
  category_id: string;
}

export interface UpdateSubcategoryRequest {
  name?: string;
  description?: string;
  is_active?: boolean;
  is_deleted?: boolean;
  category_id?: string;
}

const convertDetailsToSubcategory = (details: [string, any][]): Subcategory => {
  const subcategory: any = {};
  details.forEach(([key, value]) => {
    subcategory[key] = value;
  });
  return subcategory as Subcategory;
};

export const subcategoryService = {
  getSubcategories: async (
    categoryId?: string,
    search = "",
    skip = 0,
    limit = 100,
    is_active?: boolean | null
  ): Promise<{
    items: Subcategory[];
    pagination: {
      currentPage: number;
      totalPages: number;
      itemsPerPage: number;
      totalItems: number;
    };
  }> => {
    let url = "";
    const isActiveParam =
      typeof is_active === "boolean" ? `&is_active=${is_active}` : "";
    if (categoryId) {
      url = `/industries/${categoryId}/subcategories?search=${search}${isActiveParam}&skip=${skip}&limit=${limit}`;
    } else {
      url = `/subcategories?search=${search}${isActiveParam}&skip=${skip}&limit=${limit}`;
    }
    const response = await api.get<SubcategoryResponse>(url);
    return {
      items: response.data.data.items,
      pagination: {
        currentPage: response.data.data.pagination.current_page,
        totalPages: response.data.data.pagination.total_pages,
        itemsPerPage: response.data.data.pagination.items_per_page,
        totalItems: response.data.data.pagination.total_items,
      },
    };
  },

  getSubcategoryById: async (id: string): Promise<Subcategory> => {
    const response = await api.get<SingleSubcategoryResponse>(`/subcategories/${id}`);
    return convertDetailsToSubcategory(response.data.data.details);
  },

  createSubcategory: async (categoryId: string, subcategoryData: CreateSubcategoryRequest): Promise<Subcategory> => {
    const response = await api.post<SingleSubcategoryResponse>(`/industries/${categoryId}/subcategories`, subcategoryData);
    return convertDetailsToSubcategory(response.data.data.details);
  },

  updateSubcategory: async (id: string, subcategoryData: UpdateSubcategoryRequest): Promise<Subcategory> => {
    const response = await api.put<SingleSubcategoryResponse>(`/subcategories/${id}`, subcategoryData);
    return convertDetailsToSubcategory(response.data.data.details);
  },

  deleteSubcategory: async (id: string): Promise<boolean> => {
    const response = await api.delete<{ success: boolean }>(`/subcategories/${id}`);
    return response.data.success;
  },

  toggleSubcategoryStatus: async (id: string, is_active: boolean): Promise<Subcategory> => {
    const response = await api.put<SingleSubcategoryResponse>(`/subcategories/${id}`, { is_active });
    return convertDetailsToSubcategory(response.data.data.details);
  },
}; 