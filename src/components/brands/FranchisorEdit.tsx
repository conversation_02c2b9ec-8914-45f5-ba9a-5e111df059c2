import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  FileText,
  Edit,
  Trash2,
  Plus,
  Upload,
  Eye,
  AlertCircle,
  AlertTriangle,
  ExternalLink,
  Download,
  Loader2,
  GripVertical
} from 'lucide-react';
import { franchisorsService, Franchisor, CategoryOption } from '../../services/franchisorsService';
import { questionsService, QuestionItem } from '../../services/questionsService';
import { documentsService, type DocumentItem } from '../../services/documentsService';
import toast from 'react-hot-toast';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import Select from '../common/Select';

// Mock interfaces for sections that don't have APIs yet

// Question Item Component with proper drag and drop handling
interface QuestionItemProps {
  question: QuestionItem;
  onToggleStatus: (questionId: string) => void;
  onEdit: (question: QuestionItem) => void;
  onDelete: (questionId: string) => void;
  togglingQuestionId: string | null;
  deletingQuestionId: string | null;
}

const QuestionItemComponent: React.FC<QuestionItemProps> = ({
  question,
  onToggleStatus,
  onEdit,
  onDelete,
  togglingQuestionId,
  deletingQuestionId
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: question.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 100 : 'auto',
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-3">
      <div className="flex items-center bg-white p-4 rounded-lg border border-gray-200 group">
        <div
          className="mr-3 text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing"
          {...attributes}
          {...listeners}
        >
          <GripVertical className="w-5 h-5" />
        </div>

        <div className="flex-1">
          <div className="font-medium text-gray-900">{question.question_text}</div>
          <div className="text-sm text-gray-500 mt-1">
            <span className="capitalize">{question.question_type}</span>
            <span className="ml-2">Order: {question.order}</span>
          </div>
        </div>

        {/* Status Toggle */}
        <div className="flex items-center mr-4">
          <button
            onClick={() => onToggleStatus(question.id)}
            disabled={togglingQuestionId === question.id}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
              question.isActive ? "bg-blue-600" : "bg-gray-200"
            } ${
              togglingQuestionId === question.id
                ? "opacity-50 cursor-not-allowed"
                : "cursor-pointer"
            }`}
            title={question.isActive ? "Deactivate" : "Activate"}
          >
            {togglingQuestionId === question.id ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <Loader2
                  className={`w-4 h-4 animate-spin ${
                    question.isActive ? "text-white" : "text-gray-900"
                  }`}
                />
              </div>
            ) : (
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  question.isActive ? "translate-x-6" : "translate-x-1"
                }`}
              />
            )}
          </button>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onEdit(question);
            }}
            className="p-2 text-blue-600 hover:text-blue-800 rounded-lg hover:bg-blue-50 transition-colors"
            title="Edit question"
          >
            <Edit className="w-4 h-4" />
          </button>
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onDelete(question.id);
            }}
            disabled={deletingQuestionId === question.id}
            className="p-2 text-red-600 hover:text-red-800 rounded-lg hover:bg-red-50 disabled:opacity-50 transition-colors"
            title="Delete question"
          >
            {deletingQuestionId === question.id ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

const FranchisorEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [franchisor, setFranchisor] = useState<Franchisor | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'basic' | 'questions' | 'documents' | 'questionBank' | 'brochure'>('basic');

  // Determine if this is create mode (no id) or edit mode (has id)
  const isCreateMode = !id;

  // Section-specific loading states
  const [savingBasicInfo, setSavingBasicInfo] = useState(false);
  const [documentsLoading, setDocumentsLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [deletingDocumentId, setDeletingDocumentId] = useState<string | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  
  // Form data for basic info
  const [formData, setFormData] = useState({
    name: '',
    contactFirstName: '',
    contactLastName: '',
    email: '',
    phone: '',
    region: '',
    budget: 0,
    industry_id: '',
  });
  
  // Questions from API
  const [prequalificationQuestions, setPrequalificationQuestions] = useState<QuestionItem[]>([]);
  const [questionsLoading, setQuestionsLoading] = useState(false);
  const [deletingQuestionId, setDeletingQuestionId] = useState<string | null>(null);
  const [togglingQuestionId, setTogglingQuestionId] = useState<string | null>(null);
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<QuestionItem | null>(null);
  const [questionFormData, setQuestionFormData] = useState({
    question_text: '',
    question_type: 'text' // Default type
  });
  
  // API data for documents
  const [apiDocuments, setApiDocuments] = useState<DocumentItem[]>([]);

  // API data for question bank
  const [questionBankItems, setQuestionBankItems] = useState<{
    id: string;
    name: string;
    lead_id: string;
    franchisor_id: string | null;
    is_deleted: boolean;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  }[]>([]);
  const [questionBankLoading, setQuestionBankLoading] = useState(false);

  // Brochure state
  const [brochureUploading, setBrochureUploading] = useState(false);
  const [showBrochureUploadModal, setShowBrochureUploadModal] = useState(false);

  // Industries state
  const [industries, setIndustries] = useState<CategoryOption[]>([]);
  const [industriesLoading, setIndustriesLoading] = useState(false);

  // DnD sensors for question reordering
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  
  // Load industries
  const loadIndustries = async () => {
    setIndustriesLoading(true);
    try {
      const industriesData = await franchisorsService.getCategories();
      setIndustries(industriesData);
    } catch (error) {
      console.error('Error loading industries:', error);
      toast.error('Failed to load industries');
    } finally {
      setIndustriesLoading(false);
    }
  };

  // Load franchisor data
  useEffect(() => {
    const loadFranchisor = async () => {
      if (isCreateMode) {
        // In create mode, just set loading to false
        setLoading(false);
        return;
      }

      if (!id) return;

      setLoading(true);
      try {
        const franchisorData = await franchisorsService.getById(id);
        setFranchisor(franchisorData);

        // Populate form data
        setFormData({
          name: franchisorData.name || '',
          contactFirstName: franchisorData.contactFirstName || '',
          contactLastName: franchisorData.contactLastName || '',
          email: franchisorData.email || '',
          phone: franchisorData.phone || '',
          region: franchisorData.region || '',
          budget: franchisorData.budget || 0,
          industry_id: franchisorData.industry_id || '',
        });
      } catch (error) {
        console.error('Error loading franchisor:', error);
        toast.error('Failed to load franchisor details');
      } finally {
        setLoading(false);
      }
    };

    loadFranchisor();
    loadIndustries();
  }, [id]);
  
  // Load prequalification questions
  useEffect(() => {
    const loadQuestions = async () => {
      if (!id) return;

      setQuestionsLoading(true);
      try {
        const response = await questionsService.getList({
          page: 1,
          size: 100,
          search: null,
          franchisor_id: id,
          is_active: null,
        });

        // Map API response to QuestionItem format
        const details = (response.data as any).details;
        if (response.success && details && details.items) {
          const items = details.items.map((q: any) => ({
            id: q.id,
            franchisor_id: q.franchisor_id,
            franchisor_name: q.franchisor_name || '',
            industry: q.industry || 'prequalification',
            question_text: q.question_text,
            question_type: q.question_type,
            order: q.order || 0,
            isActive: q.is_active !== undefined ? q.is_active : false,
            created_at: q.created_at,
          }));
          setPrequalificationQuestions(items);
        }
      } catch (error) {
        console.error('Error loading questions:', error);
        toast.error('Failed to load prequalification questions');
      } finally {
        setQuestionsLoading(false);
      }
    };

    loadQuestions();
  }, [id]);
  
  // Handle form input changes
  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  // Handle save basic info
  const handleSaveBasicInfo = async () => {
    setSavingBasicInfo(true);
    try {
      if (isCreateMode) {
        // Create new franchisor
        const newFranchisor = await franchisorsService.create(formData);
        toast.success('Franchisor created successfully');

        // Navigate to the new franchisor's details page
        navigate(`/franchisors/${newFranchisor.id}`);
      } else {
        // Update existing franchisor
        if (!franchisor) return;

        await franchisorsService.update(franchisor.id, formData);
        toast.success('Basic information updated successfully');

        // Update the franchisor state with new data
        setFranchisor(prev => prev ? { ...prev, ...formData } : null);
      }
    } catch (error) {
      console.error('Error saving franchisor:', error);
      toast.error(isCreateMode ? 'Failed to create franchisor' : 'Failed to update basic information');
    } finally {
      setSavingBasicInfo(false);
    }
  };
  
  // Handle question reordering
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id && id) {
      // Optimistically update the UI first
      const oldItems = prequalificationQuestions;
      const oldIndex = oldItems.findIndex(item => item.id === active.id);
      const newIndex = oldItems.findIndex(item => item.id === over?.id);

      const newItems = arrayMove(oldItems, oldIndex, newIndex);

      // Update order property
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        order: index + 1
      }));

      // Update UI immediately
      setPrequalificationQuestions(updatedItems);

      // Prepare data for API call
      const orderUpdates = updatedItems.map((item, index) => ({
        question_id: item.id,
        order_sequence: index + 1
      }));

      try {
        // Call API to update question order
        const response = await questionsService.updateQuestionOrder(id, orderUpdates);

        if (response.success) {
          toast.success(response.description || 'Question order updated successfully');
        } else {
          throw new Error(response.description || 'Failed to update question order');
        }
      } catch (error) {
        console.error('Error updating question order:', error);
        toast.error(error instanceof Error ? error.message : 'Failed to update question order');

        // Revert the UI changes on error
        setPrequalificationQuestions(oldItems);
      }
    }
  };

  // Load documents from API
  const loadDocuments = async () => {
    if (!id) return;

    setDocumentsLoading(true);
    try {
      const response = await documentsService.getList({
        skip: 0,
        limit: 100,
        franchisor_id: id,
        is_active: null,
      });

      if (response.success && response.data.items) {
        setApiDocuments(response.data.items);
      } else {
        toast.error('Failed to load documents');
      }
    } catch (error) {
      console.error('Error loading documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setDocumentsLoading(false);
    }
  };

  // Load question bank from API
  const loadQuestionBank = async () => {
    setQuestionBankLoading(true);
    try {
      // Filter by current franchisor ID if available (not in create mode)
      const response = await questionsService.getQuestionBank(isCreateMode ? undefined : id);

      if (response.success) {
        setQuestionBankItems(response.data.items);
      } else {
        toast.error('Failed to load question bank');
      }
    } catch (error) {
      console.error('Error loading question bank:', error);
      toast.error('Failed to load question bank');
    } finally {
      setQuestionBankLoading(false);
    }
  };

  // Upload document
  const handleUploadDocument = async (uploadData: {
    file: File;
    name: string;
    description: string | null;
    is_active: boolean;
  }) => {
    if (!id) return;

    try {
      setUploadLoading(true);
      const result = await documentsService.uploadFile({
        file: uploadData.file,
        name: uploadData.name,
        description: uploadData.description,
        franchisor_id: id,
        is_active: uploadData.is_active,
      });

      if (result) {
        console.log('Document uploaded successfully:', result);
        toast.success('Document uploaded successfully');
        setShowUploadModal(false);
        // Reload documents
        loadDocuments();
      }
    } catch (error) {
      console.error('Error uploading document:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload document';
      console.log('Upload error details:', errorMessage);
      toast.error(errorMessage);
    } finally {
      setUploadLoading(false);
    }
  };

  // Upload brochure
  const handleUploadBrochure = async (file: File) => {
    if (!id) return;

    try {
      setBrochureUploading(true);
      const result = await franchisorsService.uploadBrochure(id, file);

      if (result) {
        toast.success('Brochure uploaded successfully');
        setShowBrochureUploadModal(false);
        // Update the franchisor state with new brochure URL
        setFranchisor(prev => prev ? { ...prev, brochure_url: result.brochure_url } : null);
      }
    } catch (error) {
      console.error('Error uploading brochure:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload brochure';
      toast.error(errorMessage);
    } finally {
      setBrochureUploading(false);
    }
  };

  // Delete document
  const handleDeleteDocument = async (documentId: string) => {
    try {
      setDeletingDocumentId(documentId);
      const success = await documentsService.delete(documentId);

      if (success) {
        setApiDocuments(prev => prev.filter(doc => doc.id !== documentId));
        toast.success('Document deleted successfully');
      } else {
        toast.error('Failed to delete document');
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error('Failed to delete document');
    } finally {
      setDeletingDocumentId(null);
    }
  };

  // Handle toggle question status
  const handleToggleQuestionStatus = async (questionId: string) => {
    setTogglingQuestionId(questionId);
    try {
      const response = await questionsService.toggleStatus(questionId);

      if (response.success) {
        // Update local state with the new status
        setPrequalificationQuestions(prev =>
          prev.map(q =>
            q.id === questionId
              ? { ...q, isActive: response.data.details.is_active }
              : q
          )
        );
        toast.success(response.message.description);
      } else {
        throw new Error(response.message?.description || 'Failed to toggle status');
      }
    } catch (error) {
      console.error('Error toggling question status:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to toggle question status';
      toast.error(errorMessage);
    } finally {
      setTogglingQuestionId(null);
    }
  };

  // Handle adding a new question
  const handleAddQuestion = () => {
    setEditingQuestion(null);
    setQuestionFormData({
      question_text: '',
      question_type: 'text'
    });
    setShowQuestionModal(true);
  };

  // Handle editing a question
  const handleEditQuestion = (question: QuestionItem) => {
    console.log('Edit question clicked:', question);
    setEditingQuestion(question);
    setQuestionFormData({
      question_text: question.question_text,
      question_type: question.question_type
    });
    setShowQuestionModal(true);
  };

  // Handle deleting a question
  const handleDeleteQuestion = async (questionId: string) => {
    if (!window.confirm('Are you sure you want to delete this question?')) {
      return;
    }

    setDeletingQuestionId(questionId);
    try {
      await questionsService.delete(questionId);
      toast.success('Question deleted successfully');

      // Remove the question from the list
      setPrequalificationQuestions(prev =>
        prev.filter(q => q.id !== questionId)
      );
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error('Failed to delete question');
    } finally {
      setDeletingQuestionId(null);
    }
  };

  // Handle saving question (add or edit)
  const handleSaveQuestion = async () => {
    if (!questionFormData.question_text.trim()) {
      toast.error('Question text is required');
      return;
    }

    if (!id) return;

    try {
      if (editingQuestion) {
        // TODO: Implement edit question API call
        toast('Edit question API not implemented yet');
      } else {
        // Add new question
        await questionsService.create({
          franchisor_id: id,
          question_text: questionFormData.question_text,
          question_type: questionFormData.question_type
        });

        toast.success('Question added successfully');

        // Reload questions
        const response = await questionsService.getList({
          page: 1,
          size: 100,
          search: null,
          franchisor_id: id,
          is_active: null,
        });

        const details = (response.data as any).details;
        if (response.success && details && details.items) {
          const items = details.items.map((q: any) => ({
            id: q.id,
            franchisor_id: q.franchisor_id,
            franchisor_name: q.franchisor_name || '',
            industry: q.industry || 'prequalification',
            question_text: q.question_text,
            question_type: q.question_type,
            order: q.order || 0,
            isActive: q.is_active !== undefined ? q.is_active : false,
            created_at: q.created_at,
          }));
          setPrequalificationQuestions(items);
        }
      }

      setShowQuestionModal(false);
      setEditingQuestion(null);
      setQuestionFormData({ question_text: '', question_type: 'text' });
    } catch (error) {
      console.error('Error saving question:', error);
      toast.error('Failed to save question');
    }
  };

  // Handle canceling question modal
  const handleCancelQuestion = () => {
    setShowQuestionModal(false);
    setEditingQuestion(null);
    setQuestionFormData({ question_text: '', question_type: 'text' });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading franchisor details...</span>
      </div>
    );
  }
  
  if (!franchisor) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Franchisor Not Found</h2>
        <p className="text-gray-600 mb-6">The franchisor you're looking for doesn't exist or has been removed.</p>
        <button 
          onClick={() => navigate('/franchisors')}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Back to Franchisors
        </button>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50 pb-12">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button 
                onClick={() => navigate('/franchisors')}
                className="mr-4 p-2 rounded-full hover:bg-gray-100"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <h1 className="text-2xl font-bold text-gray-900">
                {isCreateMode ? 'Add New Franchisor' : `Edit ${franchisor?.name || 'Franchisor'}`}
              </h1>
              {!isCreateMode && franchisor && (
                <>
                  {franchisor.is_active ? (
                    <span className="ml-3 px-2.5 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                      Active
                    </span>
                  ) : (
                    <span className="ml-3 px-2.5 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">
                      Inactive
                    </span>
                  )}
                </>
              )}
            </div>
          </div>
          
          {/* Tabs */}
          <div className="mt-6 border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('basic')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'basic'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Basic Information
              </button>
              {!isCreateMode && (
                <>
                  <button
                    onClick={() => setActiveTab('brochure')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'brochure'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Brochure
                  </button>
                  <button
                    onClick={() => {
                      setActiveTab('documents');
                      loadDocuments();
                    }}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'documents'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Documents
                  </button>
                  <button
                    onClick={() => setActiveTab('questions')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm ${
                      activeTab === 'questions'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    Prequalification Questions
                  </button>
                </>
              )}
              <button
                onClick={() => {
                  setActiveTab('questionBank');
                  loadQuestionBank();
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'questionBank'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Question Bank
              </button>
            </nav>
          </div>
        </div>
      </div>
      
      {/* Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        {/* Basic Information Tab */}
        {activeTab === 'basic' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Edit Brand Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Brand ID</label>
                  <div className="text-gray-500 text-sm">{franchisor.id}</div>
                </div>
                
                
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Brand Name *</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter brand name"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                  <Select
                    value={formData.industry_id}
                    onChange={(value) => handleInputChange('industry_id', value)}
                    placeholder="Select Industry"
                    options={industries.map((industry) => ({
                      value: industry.id,
                      label: industry.name,
                    }))}
                    disabled={industriesLoading}
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Region</label>
                  <Select
                    value={formData.region}
                    onChange={(v) => handleInputChange('region', v)}
                    placeholder="Select Region"
                    options={[
                      { value: 'north_america', label: 'North America' },
                      { value: 'europe', label: 'Europe' },
                      { value: 'asia_pacific', label: 'Asia Pacific' },
                      { value: 'africa', label: 'Africa' },
                      { value: 'south_america', label: 'South America' },
                    ]}
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Budget</label>
                  <input
                    type="number"
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter budget amount"
                  />
                </div>
              </div>
              
              <div>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Won ID</label>
                  <div className="text-gray-500 text-sm">{franchisor.franchisor_won_id || '-'}</div>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Contact First Name</label>
                  <input
                    type="text"
                    value={formData.contactFirstName}
                    onChange={(e) => handleInputChange('contactFirstName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter first name"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Contact Last Name</label>
                  <input
                    type="text"
                    value={formData.contactLastName}
                    onChange={(e) => handleInputChange('contactLastName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter last name"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter email address"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter phone number"
                  />
                </div>
                

              </div>
            </div>

            {/* Basic Info Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                onClick={() => navigate(`/franchisors/${franchisor.id}`)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveBasicInfo}
                disabled={savingBasicInfo}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
              >
                {savingBasicInfo && <Loader2 className="w-4 h-4 animate-spin" />}
                <span>
                  {savingBasicInfo
                    ? 'Saving...'
                    : isCreateMode
                      ? 'Create Franchisor'
                      : 'Save Changes'
                  }
                </span>
              </button>
            </div>
          </div>
        )}

        {/* Prequalification Questions Tab */}
        {activeTab === 'questions' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Prequalification Questions</h2>
              <button
                onClick={handleAddQuestion}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Question
              </button>
            </div>

            {questionsLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600">Loading questions...</span>
              </div>
            ) : (
              <>
                <div className="mb-4 text-sm text-gray-500">
                  <p>Drag and drop to reorder questions. These questions will be shown to leads during the prequalification process.</p>
                </div>

                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                  modifiers={[restrictToVerticalAxis]}
                >
                  <SortableContext
                    items={prequalificationQuestions.map(q => q.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    {prequalificationQuestions.map(question => (
                      <QuestionItemComponent
                        key={question.id}
                        question={question}
                        onToggleStatus={handleToggleQuestionStatus}
                        onEdit={handleEditQuestion}
                        onDelete={handleDeleteQuestion}
                        togglingQuestionId={togglingQuestionId}
                        deletingQuestionId={deletingQuestionId}
                      />
                    ))}
                  </SortableContext>
                </DndContext>

                {prequalificationQuestions.length === 0 && (
                  <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 mb-1">No questions yet</h3>
                    <p className="text-gray-500 mb-4">Add prequalification questions for this brand</p>
                    <button
                      onClick={handleAddQuestion}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 inline-flex items-center"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add First Question
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {/* Documents Tab */}
        {activeTab === 'documents' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Documents</h2>
              <button
                onClick={() => setShowUploadModal(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload Document
              </button>
            </div>

            {documentsLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading documents...</span>
              </div>
            ) : (
              <>
                {apiDocuments.length > 0 ? (
                  <div className="overflow-hidden border border-gray-200 rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Name
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Type
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Size
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Uploaded
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {apiDocuments.map((doc) => (
                          <tr key={doc.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <FileText className="w-5 h-5 text-gray-400 mr-3" />
                                <div>
                                  <div className="text-sm font-medium text-gray-900">{doc.name}</div>
                                  {doc.description && (
                                    <div className="text-sm text-gray-500">{doc.description}</div>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {doc.file_type}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {doc.file_size}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                doc.is_active
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {doc.is_active ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(doc.created_at).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end space-x-2">
                                <button
                                  onClick={() => window.open(doc.file_url, '_blank')}
                                  className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                                  title="View document"
                                >
                                  <Eye className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleDeleteDocument(doc.id)}
                                  disabled={deletingDocumentId === doc.id}
                                  className="text-red-600 hover:text-red-900 disabled:opacity-50 p-1 rounded hover:bg-red-50"
                                  title="Delete document"
                                >
                                  {deletingDocumentId === doc.id ? (
                                    <Loader2 className="w-4 h-4 animate-spin" />
                                  ) : (
                                    <Trash2 className="w-4 h-4" />
                                  )}
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 mb-1">No documents yet</h3>
                    <p className="text-gray-500 mb-4">Upload documents for this franchisor</p>
                    <button
                      onClick={() => setShowUploadModal(true)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 inline-flex items-center"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Upload First Document
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {/* Question Bank Tab */}
        {activeTab === 'questionBank' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Question Bank</h2>
            </div>

            <div className="mb-4 text-sm text-gray-500">
              <p>Question bank contains reusable questions for this franchisor.</p>
            </div>

            {questionBankLoading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600">Loading question bank...</span>
              </div>
            ) : questionBankItems.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500 text-lg">No questions found</div>
                <div className="text-gray-400 text-sm mt-1">
                  The question bank is empty
                </div>
              </div>
            ) : (
              <div className="overflow-hidden border border-gray-200 rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Question Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {questionBankItems.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="text-sm font-medium text-gray-900">{item.name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {item.is_active ? (
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                              Active
                            </span>
                          ) : (
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                              Inactive
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(item.created_at).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Brochure Tab */}
        {activeTab === 'brochure' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Brand Brochure</h2>
              {franchisor?.brochure_url && (
                <button
                  onClick={() => setShowBrochureUploadModal(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Replace Brochure
                </button>
              )}
            </div>

            {franchisor?.brochure_url ? (
              <div className="space-y-6">
                {/* Warning message for edit mode */}
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <div className="flex">
                    <AlertTriangle className="w-5 h-5 text-amber-400 mr-3 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium text-amber-800">
                        Important Notice
                      </h3>
                      <p className="text-sm text-amber-700 mt-1">
                        Uploading a new brochure will permanently replace the current one.
                        The previous brochure cannot be retrieved once replaced.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Brochure Preview */}
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="text-sm font-medium text-gray-900">Current Brochure</h3>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center justify-center bg-gray-100 rounded-lg h-96">
                      <iframe
                        src={franchisor.brochure_url}
                        className="w-full h-full rounded-lg"
                        title="Brand Brochure Preview"
                      />
                    </div>
                    <div className="mt-4 flex justify-center space-x-3">
                      <a
                        href={franchisor.brochure_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View Full Size
                      </a>
                      <a
                        href={franchisor.brochure_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        download
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Download
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="text-gray-500">
                  <FileText className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-1">No Brochure Added</h3>
                  <p className="text-gray-500 mb-6">Upload a brochure to showcase your brand</p>
                  <button
                    onClick={() => setShowBrochureUploadModal(true)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center mx-auto"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Brochure
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Question Modal */}
      {showQuestionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {editingQuestion ? 'Edit Question' : 'Add New Question'}
            </h3>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Question Text *
              </label>
              <textarea
                value={questionFormData.question_text}
                onChange={(e) => setQuestionFormData(prev => ({ ...prev, question_text: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Enter question text"
              />
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Question Type
              </label>
              <Select
                  value={questionFormData.question_type}
                  onChange={(value) => setQuestionFormData(prev => ({ ...prev, question_type: value }))}
                  placeholder="Select Question Type"
                  options={[
                    { value: 'text', label: 'Text' },
                    { value: 'number', label: 'Number' },
                    { value: 'boolean', label: 'Yes/No' },
                    { value: 'select', label: 'Multiple Choice' },
                  ]}
                />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancelQuestion}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveQuestion}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                {editingQuestion ? 'Update' : 'Add'} Question
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Brochure Upload Modal */}
      {showBrochureUploadModal && (
        <BrochureUploadModal
          onClose={() => setShowBrochureUploadModal(false)}
          onUpload={handleUploadBrochure}
          loading={brochureUploading}
        />
      )}

      {/* Upload Document Modal */}
      {showUploadModal && (
        <DocumentUploadModal
          onClose={() => setShowUploadModal(false)}
          onUpload={handleUploadDocument}
          loading={uploadLoading}
        />
      )}
    </div>
  );
};

// Document Upload Modal Component
interface DocumentUploadModalProps {
  onClose: () => void;
  onUpload: (data: {
    file: File;
    name: string;
    description: string | null;
    is_active: boolean;
  }) => void;
  loading: boolean;
}

const DocumentUploadModal: React.FC<DocumentUploadModalProps> = ({
  onClose,
  onUpload,
  loading,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [file, setFile] = useState<File | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      if (!formData.name) {
        // Auto-fill name from filename without extension
        const nameWithoutExt = selectedFile.name.replace(/\.[^/.]+$/, '');
        setFormData(prev => ({ ...prev, name: nameWithoutExt }));
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;

    onUpload({
      file,
      name: formData.name,
      description: formData.description || null,
      is_active: true,
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Upload Document</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              File
            </label>
            <input
              type="file"
              onChange={handleFileChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Document Name
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter document name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description (Optional)
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
              placeholder="Enter description"
            />
          </div>



          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!file || !formData.name || loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Uploading...</span>
                </>
              ) : (
                <span>Upload</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Brochure Upload Modal Component
interface BrochureUploadModalProps {
  onClose: () => void;
  onUpload: (file: File) => void;
  loading: boolean;
}

const BrochureUploadModal: React.FC<BrochureUploadModalProps> = ({
  onClose,
  onUpload,
  loading,
}) => {
  const [file, setFile] = useState<File | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;
    onUpload(file);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Upload Brochure</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Brochure File
            </label>
            <input
              type="file"
              onChange={handleFileChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              accept=".pdf"
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              Supported format: PDF only
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!file || loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  <span>Uploading...</span>
                </>
              ) : (
                <span>Upload Brochure</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FranchisorEdit;
