import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2, MessageSquare, Save } from "lucide-react";
import { settingsService } from "../../services/settingsService";
import toast from "react-hot-toast";

interface GeneralMessageSectionProps {
  messageType: "out_of_reach" | "maintenance";
  title: string;
  description: string;
}

const GeneralMessagesSection = ({
  messageType,
  title,
  description,
}: GeneralMessageSectionProps) => {
  const [message, setMessage] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const fetchMessage = async () => {
    setIsLoading(true);
    try {
      const response = await settingsService.getMessageByType(messageType);
      setMessage(response.data.details.message);
    } catch (error) {
      toast.error("Failed to fetch message");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchMessage();
  }, [messageType]);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      if (!message) {
        toast.error("Message cannot be empty");
        setIsSaving(false);
        return;
      }
      const response = await settingsService.updateByType(messageType, {
        message,
      });
      toast.success(response.message.title || "Message updated successfully");
      setMessage(response.data.details.message);
      setIsEditing(false);
      fetchMessage();
    } catch (error) {
      toast.error("Failed to update message");
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
            <MessageSquare className="w-5 h-5 text-yellow-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        </div>
        <div className="flex items-center">
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={isEditing}
              onChange={() => setIsEditing(!isEditing)}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"></div>
            <span className="ml-3 text-sm font-medium text-gray-700">Edit</span>
          </label>
        </div>
      </div>

      <div className="space-y-4">
        <div className="p-4 bg-yellow-50 rounded-lg flex items-center space-x-3">
          <AlertCircle className="w-5 h-5 text-yellow-600" />
          <div>
            <p className="text-sm text-gray-700">{description}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Message *
          </label>
          <div className="flex items-center relative">
            <textarea
              value={isLoading ? "" : message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              className={`w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent ${
                isLoading ? "opacity-50" : ""
              }`}
              required
              placeholder="Enter your message here..."
              disabled={!isEditing || isLoading}
            />
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <Loader2 className="w-6 h-6 animate-spin text-yellow-500" />
              </div>
            )}
          </div>
          {isEditing && (
            <div className="mt-4 flex justify-end">
              <button
                onClick={handleSave}
                disabled={isSaving}
                className={`px-4 py-2 rounded-lg flex items-center space-x-2 ${
                  isSaving
                    ? "bg-gray-300 cursor-not-allowed"
                    : "bg-blue-600 hover:bg-blue-700 text-white"
                }`}
              >
                {isSaving ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>{isSaving ? "Saving..." : "Save Changes"}</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GeneralMessagesSection;
