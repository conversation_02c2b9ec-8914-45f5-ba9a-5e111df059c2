import React, { useState, useRef, useEffect } from "react";
import { X } from "lucide-react";

interface MultiSelectProps {
  options: { label: string; value: string }[];
  value: string[];
  onChange: (selected: string[]) => void;
  placeholder?: string;
}

const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = "Select options",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);

  const toggleOption = (val: string) => {
    if (value.includes(val)) {
      onChange(value.filter((v) => v !== val));
    } else {
      onChange([...value, val]);
    }
  };

  const removeSelected = (val: string) => {
    onChange(value.filter((v) => v !== val));
  };

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(e.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={wrapperRef}>
      <div
        className="w-full border border-gray-300 rounded px-3 py-2 text-sm bg-white cursor-pointer"
        onClick={() => setIsOpen(!isOpen)}
      >
        {value.length === 0 ? (
          <span className="text-gray-400">{placeholder}</span>
        ) : (
          <div className="flex flex-wrap gap-1">
            {value.map((val) => {
              const label = options.find((o) => o.value === val)?.label || val;
              return (
                <div
                  key={val}
                  className="flex items-center bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs"
                >
                  {label}
                  <button onClick={() => removeSelected(val)} className="ml-1">
                    <X className="w-3 h-3" />
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {isOpen && (
        <div className="absolute z-50 mt-1 w-full border border-gray-300 rounded bg-white shadow-lg max-h-48 overflow-y-auto">
          {options.map((opt) => (
            <div
              key={opt.value}
              className={`px-3 py-2 text-sm cursor-pointer hover:bg-blue-50 ${
                value.includes(opt.value) ? "bg-blue-100 text-blue-800" : ""
              }`}
              onClick={() => toggleOption(opt.value)}
            >
              {opt.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MultiSelect;
