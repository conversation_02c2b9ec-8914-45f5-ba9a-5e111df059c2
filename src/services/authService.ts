import api from "./api";
import { User } from "../types";

interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
  remember_me_token: string;
}

interface RefreshTokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface APIErrorResponse {
  error?: {
    message?: {
      description?: string;
      title?: string;
    };
  };
  message?: {
    description?: string;
    title?: string;
  };
}

interface LoginRequest {
  email_or_mobile: string;
  password: string;
  remember_me: boolean;
}

interface ResetPasswordRequest {
  email: string;
  new_password: string;
  confirm_password: string;
}

export const authService = {
  login: async (
    email_or_mobile: string,
    password: string,
    remember_me: boolean
  ): Promise<LoginResponse> => {
    const response = await api.post<LoginResponse>("/auth/login", {
      email_or_mobile,
      password,
      remember_me,
    });
    return response?.data?.data?.details;
  },

  refreshToken: async (refreshToken: string): Promise<RefreshTokenResponse> => {
    const response = await api.post<RefreshTokenResponse>("/auth/refresh", {
      refresh_token: refreshToken,
    });
    return response?.data?.data?.details;
  },

  resetPassword: async (
    email: string,
    newPassword: string,
    confirmPassword: string
  ): Promise<boolean> => {
    const response = await api.post<{ success: boolean }>(
      "/auth/reset-password",
      {
        email,
        new_password: newPassword,
        confirm_password: confirmPassword,
      }
    );
    return response.data.success;
  },

  logout: async (): Promise<void> => {
    try {
      await api.post("/auth/logout");
    } finally {
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
      localStorage.removeItem("remember_me_token");
      localStorage.removeItem("user");
      localStorage.removeItem("token_expires_at");
    }
  },


  // Forgotpassword flow
  /**
   * Step 1 - Initiate Forgot Password: Send OTP to user's email
   */
  async initiateForgotPassword(email: string) {
    const response = await api.post('/forgot-password/initiate', { email });
    return response.data;
  },

  /**
   * Step 2 - Verify OTP and receive reset code
   */
  async verifyForgotPasswordOtp(payload: { otp: string; user_id: string }) {
    const response = await api.post('/forgot-password/verify-otp', payload);
    return response.data;
  },

  /**
   * Step 3 - Reset Password with reset code
   */
  async resetForgotPassword(payload: {
    confirm_password: string;
    new_password: string;
    reset_code: string;
    user_id: string;
  }) {
    const response = await api.post('/forgot-password/reset-password', payload);
    return response.data;
  },
};
