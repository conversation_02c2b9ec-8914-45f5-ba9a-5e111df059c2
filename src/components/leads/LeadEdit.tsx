import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, X, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';
import { leadService, Lead, LeadSource, LeadStatus, UpdateLeadRequest } from '../../services/leadService';
import { questionsService, FranchisorDropdown } from '../../services/questionsService';
import ComingSoon from '../common/ComingSoon';
import SmsConversation from '../sms/SmsConversation';

const LeadEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  console.log('LeadEdit component rendered with ID:', id);
  const [lead, setLead] = useState<Lead | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [franchisors, setFranchisors] = useState<FranchisorDropdown[]>([]);
  const [leadSources, setLeadSources] = useState<LeadSource[]>([]);
  const [leadStatuses, setLeadStatuses] = useState<LeadStatus[]>([]);
  const [activeTab, setActiveTab] = useState('basic');
  const [formData, setFormData] = useState<UpdateLeadRequest>({});

  useEffect(() => {
    const loadData = async () => {
      if (!id) {
        console.error('No lead ID provided');
        return;
      }

      try {
        setLoading(true);
        const [leadRes, franchisorsRes, sourcesRes, statusesRes] = await Promise.all([
          leadService.getLeadById(id),
          questionsService.getFranchisorsDropdown(),
          leadService.getLeadSources(),
          leadService.getLeadStatuses()
        ]);

        if (leadRes?.success) {
          setLead(leadRes.data);
          setFormData({
            first_name: leadRes.data.first_name,
            last_name: leadRes.data.last_name ?? undefined,
            email: leadRes.data.email,
            phone: leadRes.data.phone,
            mobile: leadRes.data.mobile,
            location: leadRes.data.location,
            postal_code: leadRes.data.postal_code ?? undefined,
            lead_source_id: leadRes.data.lead_source_id ?? undefined,
            lead_status_id: leadRes.data.lead_status_id,
            brand_preference: leadRes.data.brand_preference,
            budget_preference: leadRes.data.budget_preference,
            franchise_interested_in: leadRes.data.franchise_interested_in ?? undefined,
            looking_for_business_opportunity_since: leadRes.data.looking_for_business_opportunity_since ?? undefined,
            skills: leadRes.data.skills ?? undefined,
            looking_to_be_owner_operator: leadRes.data.looking_to_be_owner_operator ?? undefined,
            when_looking_to_start: leadRes.data.when_looking_to_start ?? undefined,
            ethnic_background: leadRes.data.ethnic_background ?? undefined,
            funds_to_invest: leadRes.data.funds_to_invest ?? undefined,
            eoi_nda_link: leadRes.data.eoi_nda_link ?? undefined,
            work_background: leadRes.data.work_background ?? undefined,
            motivation_to_enquire: leadRes.data.motivation_to_enquire ?? undefined,
            funds_available: leadRes.data.funds_available ?? undefined,
            motivation: leadRes.data.motivation ?? undefined,
            have_run_business_before: leadRes.data.have_run_business_before ?? undefined,
            have_mortgage: leadRes.data.have_mortgage ?? undefined,
            high_net_worth: leadRes.data.high_net_worth ?? undefined,
            is_active: leadRes.data.is_active
          });
        }

        if (franchisorsRes?.success) {
          setFranchisors(franchisorsRes.data.details.franchisors);
        }

        if (sourcesRes?.success && Array.isArray(sourcesRes.data)) {
          setLeadSources(sourcesRes.data);
        } else {
          console.error('Invalid lead sources response:', sourcesRes);
          setLeadSources([]);
        }

        if (statusesRes?.success && Array.isArray(statusesRes.data)) {
          setLeadStatuses(statusesRes.data);
        } else {
          console.error('Invalid lead statuses response:', statusesRes);
          setLeadStatuses([]);
        }
      } catch (error) {
        console.error('Error loading lead details:', error);
        toast.error('Failed to load lead details');
        // Navigate back to leads list if there's an error
        navigate('/leads');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id]);

  const handleSave = async (section: 'basic' | 'buyer') => {
    if (!id) return;
    
    try {
      setSaving(true);
      const response = await leadService.updateLead(id, formData);
      
      if (response?.success) {
        toast.success('Lead updated successfully');
        setLead(response.data);
      }
    } catch (error) {
      console.error('Error updating lead:', error);
      toast.error('Failed to update lead');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (!lead) return;
    
    // Reset form data to original values
    setFormData({
      first_name: lead.first_name,
      last_name: lead.last_name ?? undefined,
      email: lead.email,
      phone: lead.phone,
      mobile: lead.mobile,
      location: lead.location,
      postal_code: lead.postal_code ?? undefined,
      lead_source_id: lead.lead_source_id ?? undefined,
      lead_status_id: lead.lead_status_id,
      brand_preference: lead.brand_preference,
      budget_preference: lead.budget_preference,
      franchise_interested_in: lead.franchise_interested_in ?? undefined,
      looking_for_business_opportunity_since: lead.looking_for_business_opportunity_since ?? undefined,
      skills: lead.skills ?? undefined,
      looking_to_be_owner_operator: lead.looking_to_be_owner_operator ?? undefined,
      when_looking_to_start: lead.when_looking_to_start ?? undefined,
      ethnic_background: lead.ethnic_background ?? undefined,
      funds_to_invest: lead.funds_to_invest ?? undefined,
      eoi_nda_link: lead.eoi_nda_link ?? undefined,
      work_background: lead.work_background ?? undefined,
      motivation_to_enquire: lead.motivation_to_enquire ?? undefined,
      funds_available: lead.funds_available ?? undefined,
      motivation: lead.motivation ?? undefined,
      have_run_business_before: lead.have_run_business_before ?? undefined,
      have_mortgage: lead.have_mortgage ?? undefined,
      high_net_worth: lead.high_net_worth ?? undefined,
      is_active: lead.is_active
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading lead details...</div>
        </div>
      </div>
    );
  }

  if (!lead) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center py-12">
          <div className="text-gray-500">Lead not found (ID: {id})</div>
          <button
            onClick={() => navigate('/leads')}
            className="mt-4 text-blue-600 hover:text-blue-800"
          >
            Back to Leads
          </button>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'basic', name: 'Basic Information' },
    { id: 'buyer', name: 'Franchise Buyer Info' },
    { id: 'sms', name: 'SMS Conversations' },
    { id: 'meetings', name: 'Meeting Bookings' }
  ];

  // Simple test to see if component renders
  if (!id) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-red-500">No lead ID provided in URL</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(`/leads/${id}`)}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />

              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Edit {`${lead.first_name || ''} ${lead.last_name || ''}`.trim() || 'Lead'}
                </h1>
                <p className="text-sm text-gray-500">
                  Lead ID: {lead.id}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                lead.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {lead.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <nav className="flex space-x-8" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        {/* Basic Information Tab */}
        {activeTab === 'basic' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Basic Information</h2>
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2"
                >
                  <X className="w-4 h-4" />
                  <span>Cancel</span>
                </button>
                <button
                  onClick={() => handleSave('basic')}
                  disabled={saving}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
                >
                  {saving ? <Loader2 className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
                  <span>{saving ? 'Saving...' : 'Save'}</span>
                </button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                  <input
                    type="text"
                    value={formData.first_name || ''}
                    onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                  <input
                    type="text"
                    value={formData.last_name || ''}
                    onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                  <input
                    type="email"
                    value={formData.email || ''}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                  <input
                    type="tel"
                    value={formData.phone || ''}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Mobile</label>
                  <input
                    type="tel"
                    value={formData.mobile || ''}
                    onChange={(e) => setFormData({ ...formData, mobile: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                  <input
                    type="text"
                    value={formData.location || ''}
                    onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Postal Code</label>
                  <input
                    type="text"
                    value={formData.postal_code || ''}
                    onChange={(e) => setFormData({ ...formData, postal_code: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Lead Source</label>
                  <select
                    value={formData.lead_source_id || ''}
                    onChange={(e) => setFormData({ ...formData, lead_source_id: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select Lead Source</option>
                    {Array.isArray(leadSources) && leadSources.map((source) => (
                      <option key={source.id} value={source.id}>
                        {source.name}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Lead Status</label>
                  <select
                    value={formData.lead_status_id || ''}
                    onChange={(e) => setFormData({ ...formData, lead_status_id: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select Status</option>
                    {Array.isArray(leadStatuses) && leadStatuses.map((status) => (
                      <option key={status.id} value={status.id}>
                        {status.name}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Brand Preference</label>
                  <select
                    value={formData.brand_preference || ''}
                    onChange={(e) => setFormData({ ...formData, brand_preference: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select a Franchisor</option>
                    {Array.isArray(franchisors) && franchisors.map((franchisor) => (
                      <option key={franchisor.id} value={franchisor.id}>
                        {franchisor.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Budget Preference</label>
                <input
                  type="text"
                  value={formData.budget_preference || ''}
                  onChange={(e) => setFormData({ ...formData, budget_preference: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={formData.is_active ? 'active' : 'inactive'}
                  onChange={(e) => setFormData({ ...formData, is_active: e.target.value === 'active' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Franchise Buyer Info Tab */}
        {activeTab === 'buyer' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Franchise Buyer Information</h2>
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2"
                >
                  <X className="w-4 h-4" />
                  <span>Cancel</span>
                </button>
                <button
                  onClick={() => handleSave('buyer')}
                  disabled={saving}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
                >
                  {saving ? <Loader2 className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
                  <span>{saving ? 'Saving...' : 'Save'}</span>
                </button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Looking for Business Opportunity Since</label>
                  <input
                    type="text"
                    value={formData.looking_for_business_opportunity_since || ''}
                    onChange={(e) => setFormData({ ...formData, looking_for_business_opportunity_since: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Skills</label>
                  <textarea
                    value={formData.skills || ''}
                    onChange={(e) => setFormData({ ...formData, skills: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Looking to be Owner Operator?</label>
                  <select
                    value={formData.looking_to_be_owner_operator || ''}
                    onChange={(e) => setFormData({ ...formData, looking_to_be_owner_operator: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                    <option value="maybe">Maybe</option>
                  </select>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">When Looking to Start</label>
                  <input
                    type="text"
                    value={formData.when_looking_to_start || ''}
                    onChange={(e) => setFormData({ ...formData, when_looking_to_start: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Ethnic Background</label>
                  <input
                    type="text"
                    value={formData.ethnic_background || ''}
                    onChange={(e) => setFormData({ ...formData, ethnic_background: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Funds to Invest</label>
                  <input
                    type="text"
                    value={formData.funds_to_invest || ''}
                    onChange={(e) => setFormData({ ...formData, funds_to_invest: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">EOI/NDA Link</label>
                  <input
                    type="url"
                    value={formData.eoi_nda_link || ''}
                    onChange={(e) => setFormData({ ...formData, eoi_nda_link: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div>
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Work Background</label>
                  <textarea
                    value={formData.work_background || ''}
                    onChange={(e) => setFormData({ ...formData, work_background: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Motivation to Enquire</label>
                  <textarea
                    value={formData.motivation_to_enquire || ''}
                    onChange={(e) => setFormData({ ...formData, motivation_to_enquire: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Funds Available</label>
                  <input
                    type="text"
                    value={formData.funds_available || ''}
                    onChange={(e) => setFormData({ ...formData, funds_available: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Motivation</label>
                  <textarea
                    value={formData.motivation || ''}
                    onChange={(e) => setFormData({ ...formData, motivation: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Have Run Business Before?</label>
                  <select
                    value={formData.have_run_business_before || ''}
                    onChange={(e) => setFormData({ ...formData, have_run_business_before: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                  </select>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Have Mortgage?</label>
                  <select
                    value={formData.have_mortgage || ''}
                    onChange={(e) => setFormData({ ...formData, have_mortgage: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                  </select>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">High Net Worth</label>
                  <select
                    value={formData.high_net_worth || ''}
                    onChange={(e) => setFormData({ ...formData, high_net_worth: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* SMS Listing Tab */}
        {activeTab === 'sms' && lead && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">SMS Conversations</h2>
              <p className="text-sm text-gray-600 mt-1">
                View and manage SMS conversations with {lead.first_name} {lead.last_name}
              </p>
            </div>
            <div className="p-0">
              <SmsConversation leadId={lead.id} />
            </div>
          </div>
        )}

        {/* Meeting Bookings Tab */}
        {activeTab === 'meetings' && (
          <ComingSoon
            title="Meeting Bookings"
            description="Meeting booking functionality is currently under development and will be available soon."
            // features={[
            //   "Schedule meetings with leads",
            //   "Calendar integration",
            //   "Meeting reminders",
            //   "Video call integration"
            // ]}
          />
        )}
      </div>
    </div>
  );
};

export default LeadEdit;
