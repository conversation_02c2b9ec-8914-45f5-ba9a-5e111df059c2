import React, { useState } from 'react';
import { Filter } from 'lucide-react';
import Select from './Select';

const DropdownDemo: React.FC = () => {
  const [industryValue, setIndustryValue] = useState('');
  const [statusValue, setStatusValue] = useState('');
  const [sizeValue, setSizeValue] = useState('');

  const industryOptions = [
    { value: '', label: 'All Industries' },
    { value: 'food', label: 'Food & Beverage' },
    { value: 'retail', label: 'Retail' },
    { value: 'services', label: 'Services' },
    { value: 'health', label: 'Health & Fitness' },
    { value: 'education', label: 'Education' },
  ];

  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'pending', label: 'Pending' },
  ];

  const sizeOptions = [
    { value: 'sm', label: 'Small Size' },
    { value: 'md', label: 'Medium Size' },
    { value: 'lg', label: 'Large Size' },
  ];

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm border border-gray-200 space-y-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Improved Dropdown Components
      </h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Industry Filter (with proper arrow spacing)
          </label>
          <div className="max-w-xs">
            <Select
              value={industryValue}
              onChange={setIndustryValue}
              options={industryOptions}
              className="bg-gray-50"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Status Filter
          </label>
          <div className="max-w-xs">
            <Select
              value={statusValue}
              onChange={setStatusValue}
              options={statusOptions}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            With Custom Icon (like in booking filters)
          </label>
          <div className="max-w-xs relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
              <Filter className="h-5 w-5 text-gray-400" />
            </div>
            <Select
              value={industryValue}
              onChange={setIndustryValue}
              options={industryOptions}
              className="pl-10"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Different Sizes
          </label>
          <div className="space-y-3">
            <div className="max-w-xs">
              <p className="text-xs text-gray-500 mb-1">Small</p>
              <Select
                value={sizeValue}
                onChange={setSizeValue}
                options={sizeOptions}
                size="sm"
              />
            </div>
            <div className="max-w-xs">
              <p className="text-xs text-gray-500 mb-1">Medium (default)</p>
              <Select
                value={sizeValue}
                onChange={setSizeValue}
                options={sizeOptions}
                size="md"
              />
            </div>
            <div className="max-w-xs">
              <p className="text-xs text-gray-500 mb-1">Large</p>
              <Select
                value={sizeValue}
                onChange={setSizeValue}
                options={sizeOptions}
                size="lg"
              />
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Disabled State
          </label>
          <div className="max-w-xs">
            <Select
              value=""
              onChange={() => {}}
              options={statusOptions}
              disabled={true}
            />
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h4 className="font-medium text-green-800 mb-2">✅ Fixed Issues:</h4>
        <ul className="text-sm text-green-700 space-y-1">
          <li>• Proper spacing between dropdown arrow and border</li>
          <li>• Consistent styling across all dropdowns</li>
          <li>• Custom chevron icon instead of browser default</li>
          <li>• Support for different sizes (sm, md, lg)</li>
          <li>• Proper focus states and accessibility</li>
          <li>• Support for custom left padding (for icons)</li>
        </ul>
      </div>

      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-800 mb-2">🔧 Technical Details:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Uses `appearance-none` to remove browser default styling</li>
          <li>• Custom ChevronDown icon positioned absolutely</li>
          <li>• Proper `pr-10` padding to prevent text overlap with arrow</li>
          <li>• Consistent focus ring styling with other form elements</li>
          <li>• TypeScript interface for type safety</li>
        </ul>
      </div>
    </div>
  );
};

export default DropdownDemo;
