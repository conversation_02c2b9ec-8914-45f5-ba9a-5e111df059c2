import React, { useState } from 'react';
import { Save, AlertCircle, MessageSquare, Bell, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';
import MessagingRulesSection from './MessagingRulesSection';
import HolidaysSection from './HolidaysSection';
import GeneralMessagesSection from './GeneralMessagesSection';
import SampleCsvSection from './SampleCsvSection';
interface SettingsState {
  agentExceptionMessage: {
    enabled: boolean;
    message: string;
  };
  notifications: {
    email: boolean;
    sms: boolean;
    inApp: boolean;
  };
}

const Settings: React.FC = () => {
  const [settings, setSettings] = useState<SettingsState>({
    agentExceptionMessage: {
      enabled: true,
      message: "I'm sorry, but I don't have enough information to answer your question. A human agent will follow up with you shortly."
    },
    notifications: {
      email: true,
      sms: false,
      inApp: true
    }
  });

  const [isSaving, setIsSaving] = useState(false);


  const handleAgentExceptionMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setSettings({
      ...settings,
      agentExceptionMessage: {
        ...settings.agentExceptionMessage,
        message: e.target.value
      }
    });
  };

  const handleAgentExceptionToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSettings({
      ...settings,
      agentExceptionMessage: {
        ...settings.agentExceptionMessage,
        enabled: e.target.checked
      }
    });
  };

  const handleNotificationChange = (type: 'email' | 'sms' | 'inApp') => {
    setSettings({
      ...settings,
      notifications: {
        ...settings.notifications,
        [type]: !settings.notifications[type]
      }
    });
  };

  const handleSaveSettings = () => {
    setIsSaving(true);
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      toast.success('Settings saved successfully!');
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-1">Manage your application settings and preferences</p>
        </div>
        <button
          onClick={handleSaveSettings}
          disabled={isSaving}
          className={`px-4 py-2 rounded-lg flex items-center space-x-2 ${
            isSaving ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isSaving ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Save className="w-4 h-4" />
          )}
          <span>{isSaving ? "Saving..." : "Save Changes"}</span>
        </button>
      </div>

      {/* Messaging Rules */}
      <MessagingRulesSection />

      {/* Holiday Section */}
      <HolidaysSection />

      {/* Sample CSV Downloads Section */}
      <SampleCsvSection />

      {/* Agent Exception Message Section */}
      {/* <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-yellow-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900">Agent Exception Message</h2>
          </div>
          <div className="flex items-center">
            <label className="relative inline-flex items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer"
                checked={settings.agentExceptionMessage.enabled}
                onChange={handleAgentExceptionToggle}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-500"></div>
              <span className="ml-3 text-sm font-medium text-gray-700">
                {settings.agentExceptionMessage.enabled ? 'Enabled' : 'Disabled'}
              </span>
            </label>
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="p-4 bg-yellow-50 rounded-lg flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <p className="text-sm text-gray-700">
                The Agent Exception Message is displayed when the virtual agent cannot handle a user's query and needs to escalate to a human agent.
              </p>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
            <textarea
              value={settings.agentExceptionMessage.message}
              onChange={handleAgentExceptionMessageChange}
              rows={4}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="Enter your agent exception message here..."
              disabled={!settings.agentExceptionMessage.enabled}
            />
            <p className="text-sm text-gray-500 mt-1">
              This message will be displayed when the virtual agent needs to escalate a query to a human agent.
            </p>
          </div>
        </div>
      </div> */}

      {/* General Messages Section */}
      <GeneralMessagesSection
        messageType="out_of_reach"
        title="Out of Reach Message"
        description="This message will be displayed when the user is either unreachable or on holiday."
      />
      <GeneralMessagesSection
        messageType="maintenance"
        title="Maintenance Message"
        description="This message will be displayed during system maintenance periods."
      />

      {/* Notification Settings */}
      {/* <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <Bell className="w-5 h-5 text-purple-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900">Notification Settings</h2>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">Email Notifications</h3>
              <p className="text-sm text-gray-500">Receive notifications via email</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer"
                checked={settings.notifications.email}
                onChange={() => handleNotificationChange('email')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">SMS Notifications</h3>
              <p className="text-sm text-gray-500">Receive notifications via SMS</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer"
                checked={settings.notifications.sms}
                onChange={() => handleNotificationChange('sms')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h3 className="font-medium text-gray-900">In-App Notifications</h3>
              <p className="text-sm text-gray-500">Receive notifications within the application</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer"
                checked={settings.notifications.inApp}
                onChange={() => handleNotificationChange('inApp')}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default Settings;