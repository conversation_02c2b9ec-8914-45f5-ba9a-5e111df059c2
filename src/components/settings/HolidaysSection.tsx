import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
} from "@tanstack/react-table";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Calendar,
  Clock,
  ChevronLeft,
  ChevronRight,
  Loader2,
} from "lucide-react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import toast from "react-hot-toast";
import {
  settingsService,
  HolidayItem,
  HolidayFilters,
  CreateHolidayPayload,
  UpdateHolidayPayload,
} from "../../services/settingsService";
import DeleteConfirmModal from "../common/DeleteConfirmModal";
import Pagination from "../common/Pagination";
import Select from "../common/Select";

const HolidaysSection: React.FC = () => {
  // State management
  const [holidays, setHolidays] = useState<HolidayItem[]>([]);
  const [loading, setLoading] = useState({
    table: false,
    page: true,
    actions: false,
  });
  const [togglingStatus, setTogglingStatus] = useState<string | null>(null);
  const [deletingHoliday, setDeletingHoliday] = useState<string | null>(null);
  const [savingHoliday, setSavingHoliday] = useState(false);

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [editingHoliday, setEditingHoliday] = useState<HolidayItem | null>(
    null
  );

  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const [selectedHolidayType, setSelectedHolidayType] = useState<
    "PREDEFINED" | "PERSONAL" | ""
  >("");
  const [isActiveFilter, setIsActiveFilter] = useState<boolean | null>(null);

  // Pagination states
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [totalItems, setTotalItems] = useState(0);

  // Delete modal state
  const [deleteModal, setDeleteModal] = useState<{
    open: boolean;
    id: string | null;
  }>({ open: false, id: null });

  // Helper to format date as YYYY-MM-DD in local time
  function formatLocalDate(date: Date | null) {
    if (!date) return undefined;
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  }

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      if (searchTerm !== debouncedSearchTerm) {
        setPagination((prev) => ({ ...prev, pageIndex: 0 }));
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, debouncedSearchTerm]);

  // Load data
  const loadData = useCallback(async () => {
    try {
      const startDate = formatLocalDate(dateRange[0]);
      const endDate = formatLocalDate(dateRange[1]);
      if ((startDate && !endDate) || (!startDate && endDate)) return;
      setLoading((prev) => ({ ...prev, table: true }));

      const filters: HolidayFilters = {
        skip: pagination.pageIndex * pagination.pageSize,
        limit: pagination.pageSize,
        search: debouncedSearchTerm || undefined,
        holiday_type: selectedHolidayType || undefined,
        start_date: startDate,
        end_date: endDate,
      };

      const response = await settingsService.getHolidays(filters);
      if (response.data) {
        setHolidays(response.data.items);
        setTotalItems(response.data.total_count);
      }
    } catch (error) {
      console.error("Error fetching holidays:", error);
      toast.error("Failed to load holidays");
    } finally {
      setLoading((prev) => ({ ...prev, table: false, page: false }));
    }
  }, [
    pagination.pageIndex,
    pagination.pageSize,
    debouncedSearchTerm,
    selectedHolidayType,
    dateRange,
  ]);

  // Call API when any filter changes (including either date)
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Table columns
  const columns = useMemo<ColumnDef<HolidayItem>[]>(
    () => [
      {
        accessorKey: "date",
        header: "Date",
        cell: (info) => {
          const date = new Date(info.getValue() as string);
          return (
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <span>{date.toLocaleDateString()}</span>
            </div>
          );
        },
      },
      {
        accessorKey: "description",
        header: "Description",
        cell: (info) => (
          <div className="font-medium text-gray-900">
            {info.getValue() as string}
          </div>
        ),
      },
      {
        accessorKey: "holiday_type",
        header: "Type",
        cell: (info) => (
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              info.getValue() === "PREDEFINED"
                ? "bg-purple-100 text-purple-800"
                : "bg-blue-100 text-blue-800"
            }`}
          >
            {info.getValue() as string}
          </span>
        ),
      },
      {
        accessorKey: "all_day",
        header: "Time",
        cell: (info) => {
          const holiday = info.row.original;
          return (
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-gray-500" />
              <span>
                {holiday.all_day
                  ? "All Day"
                  : `${holiday.start_time?.slice(
                      0,
                      5
                    )} - ${holiday.end_time?.slice(0, 5)}`}
              </span>
            </div>
          );
        },
      },
      // {
      //   accessorKey: 'is_active',
      //   header: 'Status',
      //   cell: (info) => {
      //     const isActive = info.getValue() as boolean;
      //     const id = info.row.original.id;
      //     const isToggling = togglingStatus === id;

      //     return (
      //       <button
      //         onClick={() => handleToggleStatus(id, isActive)}
      //         disabled={isToggling}
      //         className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
      //           isActive ? 'bg-blue-600' : 'bg-gray-200'
      //         } ${isToggling ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
      //         title={isActive ? 'Deactivate' : 'Activate'}
      //       >
      //         {isToggling ? (
      //           <div className="absolute inset-0 flex items-center justify-center">
      //             <Loader2 className={`w-4 h-4 animate-spin ${isActive ? 'text-white' : 'text-gray-900'}`} />
      //           </div>
      //         ) : (
      //           <span
      //             className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
      //               isActive ? 'translate-x-6' : 'translate-x-1'
      //             }`}
      //           />
      //         )}
      //       </button>
      //     );
      //   },
      // },
      {
        id: "actions",
        header: "Actions",
        cell: (info) => (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleEdit(info.row.original)}
              className="text-blue-600 hover:text-blue-800 p-1"
              title="Edit"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleDeleteRequest(info.row.original.id)}
              disabled={deletingHoliday === info.row.original.id}
              className="text-red-600 hover:text-red-800 p-1"
              title="Delete"
            >
              {deletingHoliday === info.row.original.id ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4" />
              )}
            </button>
          </div>
        ),
      },
    ],
    [togglingStatus, deletingHoliday]
  );

  // Table setup
  const table = useReactTable({
    data: holidays,
    columns,
    pageCount: Math.ceil(totalItems / pagination.pageSize),
    state: {
      pagination: {
        pageIndex: pagination.pageIndex,
        pageSize: pagination.pageSize,
      },
    },
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
  });

  // Handlers
  const handleEdit = useCallback((holiday: HolidayItem) => {
    setEditingHoliday(holiday);
    setShowModal(true);
  }, []);

  const handleDeleteRequest = useCallback((id: string) => {
    setDeleteModal({ open: true, id });
  }, []);

  const handleDeleteConfirm = useCallback(async () => {
    if (!deleteModal.id) return;
    try {
      setDeletingHoliday(deleteModal.id);
      await settingsService.deleteHoliday(deleteModal.id);
      toast.success("Holiday deleted successfully");
      loadData();
    } catch (error) {
      console.error("Failed to delete holiday:", error);
      toast.error("Failed to delete holiday");
    } finally {
      setDeletingHoliday(null);
      setDeleteModal({ open: false, id: null });
    }
  }, [deleteModal.id, loadData]);

  const handleToggleStatus = useCallback(
    async (id: string, currentStatus: boolean) => {
      try {
        setTogglingStatus(id);
        const holiday = holidays.find((h) => h.id === id);
        if (!holiday) return;

        await settingsService.updateHoliday(id, {
          ...holiday,
          is_active: !currentStatus,
        });

        toast.success(
          `Holiday ${currentStatus ? "deactivated" : "activated"} successfully`
        );
        loadData();
      } catch (error) {
        console.error("Failed to toggle holiday status:", error);
        toast.error("Failed to update holiday status");
      } finally {
        setTogglingStatus(null);
      }
    },
    [holidays, loadData]
  );

  const clearFilters = useCallback(() => {
    setSearchTerm("");
    setDateRange([null, null]);
    setSelectedHolidayType("");
    // setIsActiveFilter(null);
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, []);

  const isFilterActive =
  searchTerm.trim() !== "" ||
  (dateRange[0] !== null || dateRange[1] !== null) ||
  selectedHolidayType !== "";


  if (loading.page && holidays.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <div className="text-gray-600">Loading holidays...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <Calendar className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Holidays</h2>
            <p className="text-sm text-gray-500">
              Manage holidays and out-of-office schedules
            </p>
          </div>
        </div>
        <button
          onClick={() => {
            setEditingHoliday(null);
            setShowModal(true);
          }}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Holiday</span>
        </button>
      </div>

      {/* Filters */}
      <div className="space-y-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-grow min-w-[200px]">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search holidays..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div className="w-full md:w-[200px]">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date Range
            </label>
            <div className="flex items-center space-x-2">
              <DatePicker
                selectsRange
                startDate={dateRange[0]}
                endDate={dateRange[1]}
                onChange={(update) => setDateRange(update)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholderText="Select date range"
              />
            </div>
          </div>

          <div className="w-full md:w-[200px]">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Holiday Type
            </label>
            <Select
              value={selectedHolidayType}
              onChange={(value) =>
                setSelectedHolidayType(value as "PREDEFINED" | "PERSONAL" | "")
              }
              placeholder="Select holiday type"
              options={[
                { value: "", label: "All Types" },
                { value: "PREDEFINED", label: "Predefined" },
                { value: "PERSONAL", label: "Personal" },
              ]}
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={clearFilters}
              disabled={!isFilterActive}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-600 flex items-center space-x-2 relative"
            >
              <Filter className="w-4 h-4" />
              <span>Clear</span>

              {isFilterActive && (
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="text-left py-3.5 px-4 text-sm font-medium text-gray-900"
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {loading.table ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className="py-8 text-center text-gray-500"
                >
                  <div className="flex items-center justify-center py-6">
                    <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                    <span className="ml-2 text-gray-600">
                      Loading holidays...
                    </span>
                  </div>
                </td>
              </tr>
            ) : holidays.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className="py-12 text-center text-gray-500"
                >
                  No holidays found
                </td>
              </tr>
            ) : (
              table.getRowModel().rows.map((row) => (
                <tr key={row.id} className="hover:bg-gray-50">
                  {row.getVisibleCells().map((cell) => (
                    <td key={cell.id} className="py-4 px-4">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <Pagination
        pageIndex={pagination.pageIndex}
        pageSize={pagination.pageSize}
        totalCount={totalItems}
        pageCount={table.getPageCount()}
        onPageChange={(page) =>
          setPagination((prev) => ({ ...prev, pageIndex: page }))
        }
        onPrevious={() => table.previousPage()}
        onNext={() => table.nextPage()}
        canPreviousPage={table.getCanPreviousPage()}
        canNextPage={table.getCanNextPage()}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        open={deleteModal.open}
        onCancel={() => setDeleteModal({ open: false, id: null })}
        onConfirm={handleDeleteConfirm}
        loading={!!deletingHoliday}
        title="Delete Holiday"
        message="Are you sure you want to delete this holiday? This action cannot be undone."
      />

      {/* Add/Edit Holiday Modal */}
      {showModal && (
        <HolidayModal
          holiday={editingHoliday}
          onClose={() => setShowModal(false)}
          onSave={async (data) => {
            try {
              setSavingHoliday(true);
              if (editingHoliday) {
                const updatePayload = data as UpdateHolidayPayload;
                await settingsService.updateHoliday(
                  editingHoliday.id,
                  updatePayload
                );
                toast.success("Holiday updated successfully");
              } else {
                const createPayload = data as CreateHolidayPayload;
                await settingsService.createHoliday(createPayload);
                toast.success("Holiday created successfully");
              }
              setShowModal(false);
              loadData();
            } catch (error) {
              console.error("Failed to save holiday:", error);
              toast.error("Failed to save holiday");
            } finally {
              setSavingHoliday(false);
            }
          }}
          saving={savingHoliday}
        />
      )}
    </div>
  );
};

interface HolidayModalProps {
  holiday: HolidayItem | null;
  onClose: () => void;
  onSave: (data: CreateHolidayPayload | UpdateHolidayPayload) => Promise<void>;
  saving: boolean;
}

const HolidayModal: React.FC<HolidayModalProps> = ({
  holiday,
  onClose,
  onSave,
  saving,
}) => {
  const [formData, setFormData] = useState({
    date: holiday?.date ? new Date(holiday.date) : new Date(),
    description: holiday?.description || "",
    holiday_type: holiday?.holiday_type || "PERSONAL",
    all_day:
      holiday?.holiday_type === "PREDEFINED" ? true : holiday?.all_day ?? false,
    start_time: holiday?.start_time || "09:00",
    end_time: holiday?.end_time || "17:00",
    is_active: holiday?.is_active ?? true,
  });

  const normalizeTime = (time: string) => {
    const parts = time.split(":");

    if (parts.length === 2) {
      return `${time}:00`; // "09:00" → "09:00:00"
    }

    if (parts.length === 3) {
      return time; // "09:00:00" → keep as-is
    }

    if (parts.length === 4) {
      // "09:00:00:00" → just take first 3 parts
      return parts.slice(0, 3).join(":");
    }

    throw new Error(`Invalid time format: ${time}`);
  };

  // Ensure all_day is always true for PREDEFINED
  useEffect(() => {
    if (formData.holiday_type === "PREDEFINED" && !formData.all_day) {
      setFormData((prev) => ({ ...prev, all_day: true }));
    }
  }, [formData.holiday_type]);

  const validateTimeRange = () => {
    if (formData.holiday_type === "PERSONAL" && !formData.all_day) {
      const start = formData.start_time;
      const end = formData.end_time;
      return start < end;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.description.trim()) {
      toast.error("Please enter a description");
      return;
    }

    if (!validateTimeRange()) {
      toast.error("End time must be greater than start time");
      return;
    }

    const payload: any = {
      date: formData.date.toISOString().split("T")[0],
      description: formData.description.trim(),
      holiday_type: formData.holiday_type as "PREDEFINED" | "PERSONAL",
    };

    if (formData.holiday_type === "PREDEFINED") {
      payload.all_day = true;
    } else {
      payload.all_day = false;
      payload.start_time = normalizeTime(formData.start_time);
      payload.end_time = normalizeTime(formData.end_time);
    }

    if (holiday?.id) {
      const updatePayload: UpdateHolidayPayload = {
        ...payload,
        is_active: formData.is_active,
      };
      await onSave(updatePayload);
    } else {
      const createPayload: CreateHolidayPayload = payload;
      await onSave(createPayload);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {holiday ? "Edit Holiday" : "Add Holiday"}
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date *
            </label>
            <DatePicker
              selected={formData.date}
              onChange={(date) =>
                setFormData({ ...formData, date: date || new Date() })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              dateFormat="MMMM d, yyyy"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description *
            </label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter holiday description"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Holiday Type *
            </label>
            <select
              value={formData.holiday_type}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  holiday_type: e.target.value as "PREDEFINED" | "PERSONAL",
                })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="PREDEFINED">Predefined</option>
              <option value="PERSONAL">Personal</option>
            </select>
          </div>

          {/* Show All Day only for PREDEFINED, always checked and disabled */}
          {formData.holiday_type === "PREDEFINED" && (
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={true}
                  disabled
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm text-gray-700">All Day</span>
              </label>
            </div>
          )}

          {/* Show time fields only for PERSONAL */}
          {formData.holiday_type === "PERSONAL" && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Time *
                </label>
                <input
                  type="time"
                  value={formData.start_time}
                  onChange={(e) =>
                    setFormData({ ...formData, start_time: e.target.value })
                  }
                  required={formData.holiday_type === "PERSONAL"}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Time *
                </label>
                <input
                  type="time"
                  value={formData.end_time}
                  onChange={(e) =>
                    setFormData({ ...formData, end_time: e.target.value })
                  }
                  required={formData.holiday_type === "PERSONAL"}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
            >
              {saving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Saving...</span>
                </>
              ) : (
                <span>Save</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default HolidaysSection;
