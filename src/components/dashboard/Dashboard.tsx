import React, { useState, useEffect } from 'react';
import { Building2, Users, Phone, Calendar, AlertCircle, Loader2, MessageSquare, Filter } from 'lucide-react';
import { dashboardService, DashboardAnalyticsResponse } from '../../services/dashboardService';
import toast from 'react-hot-toast';
import Analytics from '../analytics/Analytics';
import Select from '../common/Select';
import DatePicker from 'react-datepicker';

const dateRangeOptions = [
  { label: 'Last 7 Days', value: 'last_7_days' },
  { label: 'Last 30 Days', value: 'last_30_days' },
  { label: 'Last 3 Months', value: 'last_3_months' },
  { label: 'Last 6 Months', value: 'last_6_months' },
  { label: 'Last Year', value: 'last_year' },
  { label: 'Custom', value: 'custom' },
];

const timePeriodOptions = [
  { label: 'Day', value: 'day' },
  { label: 'Week', value: 'week' },
  { label: 'Month', value: 'month' },
  { label: 'Year', value: 'year' },
];

const Dashboard: React.FC = () => {

  // State for API data
  const [analyticsData, setAnalyticsData] = useState<DashboardAnalyticsResponse['data'] | null>(null);

  const [loading, setLoading] = useState({
    page: true,
    analytics: false,
  });

  const [dateRangeType, setDateRangeType] = useState('');
  const [timePeriod, setTimePeriod] = useState('');
  const [customDateRange, setCustomDateRange] = useState<[Date | null, Date | null]>([null, null]);

  // Load dashboard data
  useEffect(() => {
    const filters: {
      date_range?: string;
      time_period?: string;
      custom_start?: string;
      custom_end?: string;
    } = {};

    // Custom date range selected → wait until both dates are selected
    if (dateRangeType === 'custom') {
      const [start, end] = customDateRange;
      if (!start || !end) return; // 👈 DO NOT CALL API YET

      filters.date_range = 'custom';
      filters.custom_start = start.toISOString().split('T')[0];
      filters.custom_end = end.toISOString().split('T')[0];
    }

    // Regular predefined filter
    else {
      if (dateRangeType) filters.date_range = dateRangeType;
    }

    if (timePeriod) {
      filters.time_period = timePeriod;
    }

    const loadDashboardData = async () => {
      try {
        setLoading(prev => ({ ...prev, analytics: !loading.page }));

        const data = await dashboardService.getDashboardAnalytics(filters);
        setAnalyticsData(data);
      } catch (error) {
        console.error('Error loading analytics:', error);
        toast.error(error?.response?.data?.message.description || 'Failed to load dashboard analytics');
      } finally {
        setLoading(prev => ({ ...prev, page: false, analytics: false }));
      }
    };

    loadDashboardData();
  }, [dateRangeType, timePeriod, customDateRange]);


  // Dynamic stats based on API data
const stats = [
  {
    title: 'Total Franchisors',
    value: analyticsData ? analyticsData.counts.total_franchisors.toLocaleString() : '...',
    change: analyticsData ? `${analyticsData.counts.franchisors_change_percent > 0 ? '+' : ''}${analyticsData.counts.franchisors_change_percent.toFixed(2)}%` : '...',
    icon: Building2,
    color: 'bg-blue-500',
    isPositive: analyticsData ? analyticsData.counts.franchisors_change_percent >= 0 : true,
  },
  {
    title: 'Total Leads',
    value: analyticsData ? analyticsData.counts.total_leads.toLocaleString() : '...',
    change: analyticsData ? `${analyticsData.counts.leads_change_percent > 0 ? '+' : ''}${analyticsData.counts.leads_change_percent.toFixed(2)}%` : '...',
    icon: Users,
    color: 'bg-green-500',
    isPositive: analyticsData ? analyticsData.counts.leads_change_percent >= 0 : true,
  },
  {
    title: 'Total Questions',
    value: analyticsData ? analyticsData.counts.total_meetings.toLocaleString() : '...',
    change: analyticsData ? `${analyticsData.counts.questions_change_percent > 0 ? '+' : ''}${analyticsData.counts.questions_change_percent.toFixed(2)}%` : '...',
    icon: Phone,
    color: 'bg-purple-500',
    isPositive: analyticsData ? analyticsData.counts.questions_change_percent >= 0 : true,
  },
  {
    title: 'Total Meetings',
    value: analyticsData ? analyticsData.counts.total_meetings.toLocaleString() : '...',
    change: analyticsData ? `${analyticsData.counts.meetings_change_percent > 0 ? '+' : ''}${analyticsData.counts.meetings_change_percent.toFixed(2)}%` : '...',
    icon: Calendar,
    color: 'bg-orange-500',
    isPositive: analyticsData ? analyticsData.counts.meetings_change_percent >= 0 : true,
  }
];

const recentActivities = analyticsData?.recent_activity || [];

const isFilterActive = dateRangeType !== '' || timePeriod !== '' || (customDateRange[0] && customDateRange[1]);


  // Show simple loading state during initial load
  if (loading.page) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="text-gray-600">Loading analytics...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600 mt-1">
            Welcome back! Here's what's happening with your GrowthHive today.
          </p>
        </div>
        <div className="text-right">
          {/* <p className="text-sm text-gray-500">Last updated</p> */}
          {/* <p className="text-lg font-semibold text-gray-900">{new Date().toLocaleTimeString()}</p> */}
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <Select
            options={dateRangeOptions}
            value={dateRangeType}
            placeholder="Select date range"
            onChange={(val) => {
              if(val !== 'custom') {
                setCustomDateRange([null, null]);
              }
              setDateRangeType(val)
            }}
            disabled={loading.analytics}
          />

          <Select
            options={timePeriodOptions}
            value={timePeriod}
            placeholder="Select time period"
            onChange={(val) => setTimePeriod(val)}
            disabled={loading.analytics}
          />
          {dateRangeType === 'custom' && 
          <DatePicker
            selectsRange
            startDate={customDateRange[0]}
            endDate={customDateRange[1]}
            onChange={(update: [Date | null, Date | null]) =>
              setCustomDateRange(update)
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholderText="Select custom date range"
            disabled={loading.analytics}
          />}
          {/* Clear Filters Button */}
          <div className="w-full md:w-auto flex-none">
            <button
              onClick={() => {
                setDateRangeType('');
                setTimePeriod('');
                setCustomDateRange([null, null]);
              }}
              disabled={!isFilterActive}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 text-gray-700 w-full md:w-auto justify-center relative"
            >
              <Filter className="w-4 h-4" />
              <span>Clear Filters</span>

              {isFilterActive && (
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div
            key={index}
            className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  {stat.title}
                </p>
                <div className="flex items-center mt-2">
                  {loading.analytics ? (
                    <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                  ) : (
                    <p className="text-2xl font-bold text-gray-900">
                      {stat.value}
                    </p>
                  )}
                </div>
                <p
                  className={`text-sm mt-1 ${
                    stat.isPositive ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {stat.change} {analyticsData?.period_label ? (analyticsData.period_label.includes("Custom") ? "" : `from ${analyticsData?.period_label}`) : ""}
                </p>
              </div>
              <div
                className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}
              >
                <stat.icon className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Analytics */}
      <Analytics
        chartData={analyticsData?.chart_data || []}
        detailedData={analyticsData?.detailed_analytics || []}
        loading={loading.analytics}
      />

      {/* Recent Activity - Full Width */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Recent Activity
            </h2>
          </div>
        </div>
        <div className="p-6">
          {loading.analytics ? (
            <div className="flex items-center justify-center h-32">
              <div className="flex items-center space-x-3">
                <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                <span className="text-gray-600">
                  Loading recent activity...
                </span>
              </div>
            </div>
          ) : recentActivities.length === 0 ? (
            <div className="text-center py-12">
              <AlertCircle className="w-12 h-12 mx-auto text-gray-400 mb-3" />
              <h3 className="text-lg font-medium text-gray-900 mb-1">
                No Recent Activity
              </h3>
              <p className="text-gray-500">
                Recent activities will appear here when available.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {recentActivities.map((activity, index) => {
                // Choose icon & color based on type
                let Icon = Users;
                let color = "bg-gray-100 text-gray-600";

                switch (activity.type) {
                  case "lead":
                    Icon = Users;
                    color = "bg-green-100 text-green-600";
                    break;
                  case "franchisor":
                    Icon = Building2;
                    color = "bg-blue-100 text-blue-600";
                    break;
                  case "question":
                    Icon = MessageSquare;
                    color = "bg-purple-100 text-purple-600";
                    break;
                  case "escalation":
                    Icon = AlertCircle;
                    color = "bg-red-100 text-red-600";
                    break;
                  default:
                    break;
                }

                return (
                  <div
                    key={activity.id}
                    className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div
                      className={`w-10 h-10 ${color} rounded-full flex items-center justify-center flex-shrink-0`}
                    >
                      <Icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                          <p className="text-sm text-gray-600 truncate">{activity.description}</p>
                        </div>
                        <div className="flex items-center flex-wrap sm:flex-nowrap gap-2 sm:gap-3">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                            {activity.metadata?.status || activity.type}
                          </span>
                          <p className="text-xs text-gray-500 whitespace-nowrap">
                            {new Date(activity.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
